// إدارة حسابات WhatsApp باستخدام Electron
let whatsappManager;

class WhatsAppManager {
    constructor() {
        this.accounts = new Map();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupElectronListeners();

        // التحقق من تسجيل الدخول قبل تحميل الحسابات
        if (this.isUserLoggedIn()) {
            console.log('✅ المستخدم مسجل الدخول، بدء تحميل الحسابات');
            this.loadAccounts();
        } else {
            console.log('🔒 المستخدم غير مسجل الدخول، تأجيل تحميل الحسابات');
        }
    }

    // التحقق من حالة تسجيل الدخول
    isUserLoggedIn() {
        try {
            const userSession = localStorage.getItem('userSession');
            if (!userSession) {
                return false;
            }

            const session = JSON.parse(userSession);

            // التحقق من وجود البيانات المطلوبة
            if (!session.username || !session.loginTime || !session.userId) {
                return false;
            }

            // التحقق من تاريخ تسجيل الدخول (يجب أن يكون خلال آخر 24 ساعة)
            const loginTime = new Date(session.loginTime);
            const currentTime = new Date();
            const timeDiff = currentTime.getTime() - loginTime.getTime();
            const hoursDiff = timeDiff / (1000 * 60 * 60);

            if (hoursDiff > 24) {
                return false;
            }

            // التحقق من انتهاء صلاحية الحساب إذا كان موجوداً
            if (session.expiryDate) {
                let expiryDate;

                if (session.expiryDate.seconds) {
                    expiryDate = new Date(session.expiryDate.seconds * 1000);
                } else {
                    expiryDate = new Date(session.expiryDate);
                }

                if (currentTime > expiryDate) {
                    return false;
                }
            }

            return true;
        } catch (error) {
            console.error('خطأ في التحقق من حالة تسجيل الدخول:', error);
            return false;
        }
    }

    // تفعيل الوظائف بعد تسجيل الدخول بنجاح
    enableFunctionsAfterLogin() {
        console.log('🔓 تم تسجيل الدخول بنجاح، بدء تفعيل وظائف WhatsApp Manager');

        // بدء تحميل الحسابات
        this.loadAccounts();

        // إشعار WhatsAppManagerWeb لبدء الاتصال التلقائي
        if (window.electronAPI && window.electronAPI.enableWhatsAppFunctions) {
            window.electronAPI.enableWhatsAppFunctions();
        }
    }

    // إعداد مستمعي أحداث Electron
    setupElectronListeners() {
        if (window.electronAPI) {
            console.log('Setting up Electron listeners');

            // استقبال QR Code
            window.electronAPI.onQRCodeGenerated((event, data) => {
                console.log('QR Code event received:', data);
                this.displayQRCode(data.sessionId, data.qrCode);
            });

            // استقبال رمز الاقتران
            window.electronAPI.onPairingCodeGenerated((event, data) => {
                console.log('Pairing Code event received:', data);
                this.displayPairingCode(data.sessionId, data.code);
            });

            // نجح الاتصال
            window.electronAPI.onSessionConnected((event, data) => {
                console.log('Session connected event received:', data);
                this.onAccountReady(data.sessionId, data.userInfo);
            });

            // انقطع الاتصال
            window.electronAPI.onSessionDisconnected((event, data) => {
                console.log('Session disconnected event received:', data);
                this.onAccountDisconnected(data.sessionId);
            });

            // استقبال رسالة
            window.electronAPI.onMessageReceived((event, data) => {
                console.log('Message received event:', data);
                this.onMessageReceived(data.sessionId, data.message);
            });

            // تحميل الجلسات المحفوظة
            window.electronAPI.onSessionsLoaded((event, data) => {
                console.log('Saved sessions loaded:', data);
                this.loadSavedSessions(data.sessions);
            });

            // إعادة الاتصال بالجلسات
            window.electronAPI.onSessionReconnecting((event, data) => {
                console.log('Session reconnecting:', data);
                this.onSessionReconnecting(data.sessionId, data.message);
            });

            // فشل الاتصال
            window.electronAPI.onSessionConnectionFailed((event, data) => {
                console.log('Session connection failed:', data);
                this.onSessionConnectionFailed(data.sessionId, data.error);
            });

            // الجلسات التي تحتاج إعادة مصادقة
            window.electronAPI.onSessionAuthRequired((event, data) => {
                console.log('Session auth required:', data);
                this.onSessionAuthRequired(data.sessionId, data.userInfo, data.message, data.canReconnect);
            });

            // جاري الاتصال بالجلسة
            window.electronAPI.onSessionConnecting((event, data) => {
                console.log('Session connecting:', data);
                this.onSessionConnecting(data.sessionId, data.userInfo, data.message);
            });

            // نجح الاتصال بالجلسة
            window.electronAPI.onSessionConnected((event, data) => {
                console.log('Session connected:', data);
                this.onSessionConnected(data.sessionId, data.userInfo, data.deviceInfo, data.whatsappState, data.connectionCount, data.messageCount, data.contactCount, data.chatCount);
            });

            // انقطع الاتصال بالجلسة
            window.electronAPI.onSessionDisconnected((event, data) => {
                console.log('Session disconnected:', data);
                this.onSessionDisconnected(data.sessionId, data.userInfo, data.message);
            });

            console.log('Electron listeners setup complete');
        } else {
            console.error('electronAPI not available');
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // زر إنشاء حساب جديد
        const createBtn = document.getElementById('createAccountBtn');
        if (createBtn) {
            createBtn.addEventListener('click', () => {
                this.showCreateAccountModal();
            });
        }

        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.closeModal(e.target.closest('.modal-overlay'));
            });
        });

        // النقر خارج النافذة المنبثقة
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal);
                }
            });
        });

        // خيارات الاتصال
        document.querySelectorAll('.option-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.selectConnectionOption(btn);
            });
        });

        // نموذج إنشاء الحساب
        const createForm = document.getElementById('createAccountForm');
        if (createForm) {
            createForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.createAccount();
            });
        }

        const cancelBtn = document.getElementById('cancelCreationBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.closeModal(document.getElementById('createAccountModal'));
            });
        }
    }

    // تحميل الجلسات المحفوظة
    loadSavedSessions(sessions) {
        console.log('Loading saved sessions:', sessions);

        // مسح الحسابات الحالية أولاً
        this.accounts.clear();

        sessions.forEach(sessionData => {
            // إضافة جميع الجلسات بغض النظر عن حالتها
            this.accounts.set(sessionData.id, {
                name: sessionData.userInfo?.name || sessionData.accountName || 'حساب محفوظ',
                phone: sessionData.phoneNumber,
                status: sessionData.status,
                connectionType: sessionData.connectionType,
                messageCount: sessionData.messageCount || 0,
                contactCount: sessionData.contactCount || 0,
                chatCount: sessionData.chatCount || 0,
                lastSeen: sessionData.createdAt,
                userInfo: sessionData.userInfo
            });
        });

        this.updateAccountsDisplay();
    }

    // تحميل الحسابات المحفوظة (fallback إذا لم يتم تحميل الجلسات عبر event)
    async loadAccounts() {
        try {
            // انتظار قليل للسماح للجلسات بالتحميل عبر event listener
            setTimeout(async () => {
                // إذا لم تكن هناك حسابات محملة، جرب تحميلها مباشرة
                if (this.accounts.size === 0 && window.electronAPI) {
                    console.log('No accounts loaded via events, trying direct load...');
                    const response = await window.electronAPI.getAllSessions();
                    
                    if (response.success && Array.isArray(response.sessions)) {
                        response.sessions.forEach(session => {
                            this.accounts.set(session.id, {
                                name: session.userInfo?.name || session.accountName || session.id,
                                phone: session.phoneNumber,
                                status: session.status,
                                userInfo: session.userInfo,
                                messageCount: session.messageCount || 0,
                                contactCount: session.contactCount || 0,
                                chatCount: session.chatCount || 0,
                                lastSeen: session.lastSeen || session.createdAt
                            });
                        });
                        
                        this.updateAccountsDisplay();
                    }
                }
            }, 1000);
        } catch (error) {
            console.error('خطأ في تحميل الحسابات:', error);
            this.showNotification('خطأ في تحميل الحسابات المحفوظة', 'error');
        }
    }

    // عرض نافذة إنشاء حساب
    showCreateAccountModal() {
        const modal = document.getElementById('createAccountModal');
        if (modal) {
            modal.classList.add('active');
            
            // إعادة تعيين النموذج
            const form = document.getElementById('createAccountForm');
            if (form) {
                form.reset();
            }
            
            document.querySelectorAll('.option-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            const qrOption = document.getElementById('qrCodeOption');
            if (qrOption) {
                qrOption.classList.add('selected');
            }
        }
    }

    // إغلاق النافذة المنبثقة
    closeModal(modal) {
        if (modal) {
            modal.classList.remove('active');
            
            // إيقاف العد التنازلي إذا كان النافذة المغلقة هي نافذة رمز الاقتران
            if (modal.id === 'pairingCodeModal' && this.pairingCodeTimer) {
                clearInterval(this.pairingCodeTimer);
                this.pairingCodeTimer = null;
                console.log('Pairing code timer stopped due to modal close');
            }
        }
    }

    // اختيار طريقة الاتصال
    selectConnectionOption(selectedBtn) {
        document.querySelectorAll('.option-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        selectedBtn.classList.add('selected');
    }

    // إنشاء حساب جديد
    async createAccount() {
        const accountNameEl = document.getElementById('accountName');
        const phoneNumberEl = document.getElementById('phoneNumber');
        const selectedOption = document.querySelector('.option-btn.selected');
        
        if (!accountNameEl || !phoneNumberEl || !selectedOption) {
            this.showNotification('عناصر النموذج غير موجودة', 'error');
            return;
        }
        
        const accountName = accountNameEl.value.trim();
        let phoneNumber = phoneNumberEl.value.trim();
        const connectionType = selectedOption.dataset.type;

        if (!accountName || !phoneNumber) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // التحقق من صحة رقم الهاتف
        if (!this.validatePhoneNumber(phoneNumber)) {
            this.showNotification('رقم الهاتف غير صحيح. يجب أن يكون بتنسيق E.164 بدون علامة +', 'error');
            return;
        }
        
        // تنظيف رقم الهاتف وتحويله إلى تنسيق E.164 بدون علامة +
        phoneNumber = phoneNumber.replace(/[\s\-\(\)\+]/g, '');

        const sessionId = this.generateSessionId(accountName, phoneNumber);
        
        try {
            console.log('Creating WhatsApp session:', { sessionId, phoneNumber, accountName, connectionType });

            // إضافة الحساب إلى القائمة فوراً
            this.accounts.set(sessionId, {
                name: accountName,
                phone: phoneNumber,
                status: 'connecting',
                connectionType: connectionType,
                messageCount: 0,
                contactCount: 0,
                lastSeen: new Date().toISOString()
            });

            // إغلاق نافذة الإنشاء فوراً
            this.closeModal(document.getElementById('createAccountModal'));

            // عرض نافذة الاتصال فوراً
            if (connectionType === 'qr') {
                console.log('Starting QR connection for session:', sessionId);
                this.showQRModal(sessionId);
            } else if (connectionType === 'pairing') {
                console.log('Starting pairing code connection for session:', sessionId);
                this.showPairingModal(sessionId);
            }

            this.updateAccountsDisplay();
            this.showNotification('جاري إنشاء الحساب...', 'info');

            // إنشاء الجلسة في الخلفية
            const response = await window.electronAPI.createWhatsAppSession({
                sessionId: sessionId,
                phoneNumber: phoneNumber,
                accountName: accountName,
                connectionType: connectionType
            });

            console.log('Session creation response:', response);

            if (response.success) {
                this.showNotification('تم إنشاء الحساب بنجاح', 'success');
            } else {
                console.error('Session creation failed:', response);
                // إزالة الحساب من القائمة في حالة الفشل
                this.accounts.delete(sessionId);
                this.updateAccountsDisplay();
                this.closeModal(document.getElementById('qrCodeModal'));
                this.closeModal(document.getElementById('pairingCodeModal'));
                this.showNotification(response.message || 'خطأ في إنشاء الحساب', 'error');
            }
        } catch (error) {
            console.error('خطأ في إنشاء الحساب:', error);
            // إزالة الحساب من القائمة في حالة الخطأ
            this.accounts.delete(sessionId);
            this.updateAccountsDisplay();
            this.closeModal(document.getElementById('qrCodeModal'));
            this.closeModal(document.getElementById('pairingCodeModal'));
            this.showNotification('خطأ في إنشاء الحساب', 'error');
        }
    }

    // بدء الاتصال عبر رمز الاقتران
    async startPairingCodeConnection(sessionId, phoneNumber) {
        console.log(`Starting pairing code connection for session: ${sessionId}, phone: ${phoneNumber}`);

        // إظهار نافذة الاقتران مع حالة التحميل
        this.showPairingModal(sessionId);

        try {
            // إنشاء الجلسة أولاً
            const createResponse = await window.electronAPI.createSession({
                sessionId: sessionId,
                connectionType: 'pairing',
                phoneNumber: phoneNumber
            });

            console.log('Session creation response:', createResponse);

            if (!createResponse.success) {
                console.error('Session creation failed:', createResponse);
                this.showNotification(createResponse.message || 'خطأ في إنشاء الجلسة', 'error');
                return;
            }

            // سيتم طلب رمز الاقتران تلقائياً من خلال setupSocketEvents
            // لا حاجة لطلب يدوي هنا - Baileys سيتولى الأمر
            console.log('Session created successfully. Waiting for automatic pairing code generation...');

        } catch (error) {
            console.error('خطأ في إنشاء الجلسة:', error);
            this.showNotification('خطأ في إنشاء الجلسة', 'error');
        }
    }

    // عرض نافذة QR Code
    showQRModal(sessionId) {
        console.log(`Showing QR Modal for session: ${sessionId}`);
        const modal = document.getElementById('qrCodeModal');
        const loadingEl = document.getElementById('qrLoading');
        const containerEl = document.getElementById('qrCodeContainer');

        if (modal && loadingEl && containerEl) {
            // إظهار شاشة التحميل وإخفاء المحتوى
            loadingEl.style.display = 'block';
            containerEl.style.display = 'none';

            // تحديث نص التحميل ليكون أكثر تفاعلاً
            const loadingText = loadingEl.querySelector('p');
            if (loadingText) {
                loadingText.textContent = 'جاري تحضير رمز QR...';
            }

            // فتح النافذة فوراً
            modal.classList.add('active');
            console.log('QR Modal opened successfully');
        } else {
            console.error('QR Modal elements not found:', { modal, loadingEl, containerEl });
        }
    }

    // عرض نافذة رمز الاقتران
    showPairingModal(sessionId) {
        console.log(`Showing Pairing Modal for session: ${sessionId}`);
        const modal = document.getElementById('pairingCodeModal');
        const loadingEl = document.getElementById('pairingLoading');
        const containerEl = document.getElementById('pairingCodeContainer');

        if (modal && loadingEl && containerEl) {
            // إظهار شاشة التحميل وإخفاء المحتوى
            loadingEl.style.display = 'block';
            containerEl.style.display = 'none';

            // فتح النافذة
            modal.classList.add('active');
            console.log('Pairing Modal opened successfully');
        } else {
            console.error('Pairing Modal elements not found:', { modal, loadingEl, containerEl });
        }
    }

    // عرض QR Code
    displayQRCode(sessionId, qrCodeDataURL) {
        console.log(`Displaying QR Code for session: ${sessionId}`);
        const modal = document.getElementById('qrCodeModal');
        const loadingEl = document.getElementById('qrLoading');
        const containerEl = document.getElementById('qrCodeContainer');

        if (modal && loadingEl && containerEl) {
            // إخفاء شاشة التحميل بسرعة
            loadingEl.style.display = 'none';

            // عرض QR Code مع تحسين الأداء
            containerEl.style.display = 'block';
            containerEl.innerHTML = `
                <div class="qr-code-display">
                    <img src="${qrCodeDataURL}" alt="QR Code" class="qr-image" 
                         style="max-width: 300px; max-height: 300px; image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;" 
                         loading="eager" decoding="sync">
                    <p>امسح هذا الرمز باستخدام تطبيق WhatsApp</p>
                    <small>افتح WhatsApp > الإعدادات > الأجهزة المرتبطة > ربط جهاز</small>
                </div>
            `;

            // التأكد من أن النافذة مفتوحة
            if (!modal.classList.contains('active')) {
                modal.classList.add('active');
            }

            // إضافة تأثير انتقال سلس
            containerEl.style.opacity = '0';
            setTimeout(() => {
                containerEl.style.transition = 'opacity 0.3s ease-in-out';
                containerEl.style.opacity = '1';
            }, 10);
        } else {
            console.error('QR Code modal elements not found');
        }
    }

    // عرض رمز الاقتران
    displayPairingCode(sessionId, code) {
        console.log(`Displaying Pairing Code for session: ${sessionId}, code: ${code}`);
        const modal = document.getElementById('pairingCodeModal');
        const loadingEl = document.getElementById('pairingLoading');
        const containerEl = document.getElementById('pairingCodeContainer');

        if (modal && loadingEl && containerEl) {
            // إخفاء شاشة التحميل
            loadingEl.style.display = 'none';

            // تنسيق رمز الاقتران لعرضه بشكل أفضل (إضافة مسافات بين الأرقام)
            const formattedCode = code.split('').join(' ');

            // عرض رمز الاقتران
            containerEl.style.display = 'block';
            containerEl.innerHTML = `
                <div class="pairing-code-display">
                    <div class="code-header">
                        <h3>رمز الاقتران</h3>
                        <p class="code-info">تم توليد هذا الرمز بواسطة WhatsApp للتحقق من هويتك</p>
                    </div>
                    <div class="code-box">
                        <span class="code-text">${formattedCode}</span>
                    </div>
                    <div class="code-timer">
                        <p>ينتهي صلاحية الرمز خلال <span id="codeTimer">٦٠</span> ثانية</p>
                    </div>
                    <div class="code-instructions">
                        <p><strong>كيفية الاستخدام:</strong></p>
                        <ol>
                            <li>افتح تطبيق WhatsApp على هاتفك</li>
                            <li>اذهب إلى: الإعدادات > الأجهزة المرتبطة</li>
                            <li>انقر على "ربط جهاز"</li>
                            <li>اختر "ربط باستخدام رقم الهاتف"</li>
                            <li>أدخل الرمز: <strong>${code}</strong></li>
                        </ol>
                    </div>
                    <div class="code-actions">
                        <button class="retry-btn" onclick="whatsappManager.requestNewPairingCode('${sessionId}')">
                            طلب رمز جديد
                        </button>
                    </div>
                </div>
            `;

            // بدء العد التنازلي برمز الاقتران
            this.startPairingCodeTimer();


            // التأكد من أن النافذة مفتوحة
            if (!modal.classList.contains('active')) {
                modal.classList.add('active');
            }
        } else {
            console.error('Pairing Code modal elements not found');
        }
    }

    // عند نجاح الاتصال
    onAccountReady(sessionId, userInfo) {
        // إغلاق النوافذ المنبثقة فوراً عند نجاح الاتصال
        this.closeModal(document.getElementById('qrCodeModal'));
        this.closeModal(document.getElementById('pairingCodeModal'));
        
        const account = this.accounts.get(sessionId);
        if (account) {
            account.status = 'connected';
            account.userInfo = userInfo;
            
            this.updateAccountsDisplay();
            
            this.showNotification(`تم الاتصال بنجاح: ${userInfo.name}`, 'success');
        }
    }

    // عند نجاح الاتصال بالجلسة المحفوظة (مع البيانات المحدثة من WhatsApp)
    onSessionConnected(sessionId, userInfo, deviceInfo, whatsappState, connectionCount, messageCount, contactCount, chatCount) {
        console.log(`Session ${sessionId} connected with updated data from WhatsApp`);
        
        // إغلاق النوافذ المنبثقة فوراً عند نجاح الاتصال
        this.closeModal(document.getElementById('qrCodeModal'));
        this.closeModal(document.getElementById('pairingCodeModal'));
        
        const account = this.accounts.get(sessionId);
        if (account) {
            // تحديث بيانات الحساب بالمعلومات الجديدة من WhatsApp
            account.status = 'connected';
            account.userInfo = userInfo;
            account.deviceInfo = deviceInfo;
            account.whatsappState = whatsappState;
            account.connectionCount = connectionCount;
            account.lastConnected = new Date().toISOString();
            
            // تحديث الإحصائيات من البيانات الجديدة من WhatsApp
            account.messageCount = messageCount || 0;
            account.contactCount = contactCount || 0;
            account.chatCount = chatCount || 0;
            
            if (userInfo) {
                account.name = userInfo.name || account.name;
                account.phone = userInfo.phone || account.phone;
            }
            
            // تحديث العرض
            this.updateAccountsDisplay();
            
            this.showNotification(`تم الاتصال بنجاح: ${userInfo?.name || account.name}`, 'success');
        } else {
            console.warn(`Account ${sessionId} not found in accounts map`);
        }
    }

    // عند انقطاع الاتصال
    onAccountDisconnected(sessionId) {
        const account = this.accounts.get(sessionId);
        if (account) {
            account.status = 'disconnected';
            
            this.updateAccountsDisplay();
            
            this.showNotification(`انقطع الاتصال: ${account.name}`, 'warning');
        }
    }

    // عند استقبال رسالة
    onMessageReceived(sessionId, message) {
        // console.log('رسالة جديدة:', sessionId, message);
        // يمكن إضافة منطق معالجة الرسائل هنا
    }

    // معالجة إعادة الاتصال بالجلسة
    onSessionReconnecting(sessionId, message) {
        console.log(`Session ${sessionId} is reconnecting: ${message}`);

        const account = this.accounts.get(sessionId);
        if (account) {
            account.status = 'reconnecting';
            this.updateAccountsDisplay();
            this.showNotification(`جاري إعادة الاتصال بـ ${account.name}...`, 'info');
        }
    }

    // معالجة فشل الاتصال
    onSessionConnectionFailed(sessionId, error) {
        console.log(`Session ${sessionId} connection failed: ${error}`);

        const account = this.accounts.get(sessionId);
        if (account) {
            account.status = 'connection_failed';
            this.updateAccountsDisplay();
            this.showNotification(`فشل الاتصال بـ ${account.name}: ${error}`, 'error');
        }
    }

    // إعادة الاتصال بحساب محفوظ
    async reconnectAccount(sessionId) {
        try {
            const account = this.accounts.get(sessionId);
            if (!account) {
                this.showNotification('الحساب غير موجود', 'error');
                return;
            }

            console.log('Reconnecting to saved account:', sessionId);

            // تحدية حالة الحساب
            account.status = 'connecting';
            this.updateAccountsDisplay();

            // إعادة الاتصال
            const response = await window.electronAPI.reconnectSession(sessionId);

            if (response.success) {
                this.showNotification('جاري إعادة الاتصال...', 'info');
            } else {
                account.status = 'saved';
                this.updateAccountsDisplay();
                this.showNotification(`فشل في إعادة الاتصال: ${response.error}`, 'error');
            }

        } catch (error) {
            console.error('خطأ في إعادة الاتصال:', error);
            this.showNotification('خطأ في إعادة الاتصال', 'error');
        }
    }

    // حذف حساب
    async deleteAccount(sessionId) {
        const account = this.accounts.get(sessionId);
        if (!account) {
            this.showNotification('الحساب غير موجود', 'error');
            return;
        }

        // نافذة تأكيد محسنة
        const confirmMessage = `هل أنت متأكد من حذف الحساب؟\n\nاسم الحساب: ${account.name}\nرقم الهاتف: ${account.phone}\n\nسيتم حذف جميع البيانات المرتبطة بهذا الحساب نهائياً.`;

        if (confirm(confirmMessage)) {
            try {
                // إظهار مؤشر التحميل
                this.showNotification('جاري حذف الحساب...', 'info');

                const response = await window.electronAPI.forceDeleteAccount(sessionId);

                if (response.success) {
                    // إزالة الحساب من القائمة المحلية
                    this.accounts.delete(sessionId);

                    // تحديث العرض
                    this.updateAccountsDisplay();

                    // إظهار رسالة نجاح
                    this.showNotification(`تم حذف الحساب "${account.name}" بنجاح`, 'success');

                    // تسجيل العملية
                    console.log(`Account deleted successfully: ${sessionId}`);
                } else {
                    this.showNotification(response.message || 'خطأ في حذف الحساب', 'error');
                    console.error('Delete account failed:', response);
                }
            } catch (error) {
                console.error('خطأ في حذف الحساب:', error);
                this.showNotification('حدث خطأ أثناء حذف الحساب', 'error');
            }
        }
    }

    // تحديث عرض الحسابات
    updateAccountsDisplay() {
        // منع التحديث المتكرر
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;
        
        // حفظ البيانات في localStorage
        this.saveAccountsToStorage();
        
        const container = document.getElementById('accountsContainer');
        if (!container) {
            this.isUpdating = false;
            return;
        }
        
        if (this.accounts.size === 0) {
            // عرض الحالة الفارغة
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <svg width="120" height="120" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2M12 11a4 4 0 100-8 4 4 0 000 8z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="empty-state-content">
                        <h3>لا توجد حسابات WhatsApp</h3>
                        <p>ابدأ بإنشاء حساب جديد لإدارة رسائلك وجهات الاتصال الخاصة بك</p>
                        <button class="empty-action-btn" onclick="whatsappManager.showCreateAccountModal()">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            إنشاء أول حساب
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        // عرض شبكة الحسابات
        let html = '<div class="accounts-grid">';
        this.accounts.forEach((account, sessionId) => {
            const statusClass = this.getStatusClass(account.status);
            const statusText = this.getStatusText(account.status);
            
            html += `
                <div class="account-card ${statusClass}" data-account-id="${sessionId}" data-status="${account.status}">
                    <div class="account-header">
                        <div class="account-avatar">
                            ${account.userInfo?.profilePicture ?
                                `<img src="${account.userInfo.profilePicture}" alt="${account.name}" class="avatar-image">` :
                                `<div class="avatar-placeholder">
                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2M12 11a4 4 0 100-8 4 4 0 000 8z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>`
                            }
                        </div>
                        <div class="account-info">
                            <h4 class="account-name">${account.name}</h4>
                            <p class="account-phone">${this.formatPhoneNumber(account.phone)}</p>
                            ${account.userInfo ? `<p class="whatsapp-name">${account.userInfo.name}</p>` : ''}
                            <small class="last-seen">${this.getLastSeenText(account.lastSeen)}</small>
                        </div>
                        <div class="account-status">
                            <div class="status-indicator ${statusClass}"></div>
                            <span class="status-text">${statusText}</span>
                        </div>
                    </div>
                    <div class="account-stats">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z" stroke="currentColor" stroke-width="1.5"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <span class="stat-number">${account.messageCount || 0}</span>
                                <span class="stat-label">رسالة</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75" stroke="currentColor" stroke-width="1.5"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <span class="stat-number">${account.contactCount || 0}</span>
                                <span class="stat-label">جهة اتصال</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" stroke="currentColor" stroke-width="1.5"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <span class="stat-number">${account.chatCount || 0}</span>
                                <span class="stat-label">محادثة</span>
                            </div>
                        </div>
                    </div>
                    <div class="account-actions">
                        <button class="action-btn primary" onclick="whatsappManager.openChat('${sessionId}')" title="فتح المحادثة">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>محادثة</span>
                        </button>
                        <button class="action-btn secondary" onclick="whatsappManager.viewAccountDetails('${sessionId}')" title="التفاصيل">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            <span>تفاصيل</span>
                        </button>
                        <button class="action-btn danger" onclick="whatsappManager.deleteAccount('${sessionId}')" title="حذف الحساب">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6M10 11v6M14 11v6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>حذف</span>
                        </button>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        container.innerHTML = html;
        
        // إنهاء عملية التحديث
        setTimeout(() => {
            this.isUpdating = false;
        }, 100);
    }

    // حفظ بيانات الحسابات في localStorage
    saveAccountsToStorage() {
        const accountsArray = Array.from(this.accounts.entries()).map(([sessionId, account]) => ({
            sessionId,
            ...account
        }));
        localStorage.setItem('whatsapp-accounts', JSON.stringify(accountsArray));
    }



    // الحصول على فئة الحالة
    getStatusClass(status) {
        switch (status) {
            case 'connected': return 'status-connected';
            case 'connecting': return 'status-connecting';
            case 'qr_ready': return 'status-qr-ready';
            case 'disconnected': return 'status-disconnected';
            default: return 'status-unknown';
        }
    }

    // الحصول على نص الحالة
    getStatusText(status) {
        switch (status) {
            case 'connected': return 'متصل';
            case 'connecting': return 'جاري الاتصال';
            case 'qr_ready': return 'في انتظار المسح';
            case 'disconnected': return 'غير متصل';
            default: return 'غير معروف';
        }
    }

    // الحصول على نص آخر ظهور
    getLastSeenText(lastSeen) {
        if (!lastSeen) return '';

        const now = new Date();
        const lastSeenDate = new Date(lastSeen);
        const diffMs = now - lastSeenDate;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / ********);

        if (diffMins < 1) return 'الآن';
        if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
        if (diffHours < 24) return `منذ ${diffHours} ساعة`;
        if (diffDays < 7) return `منذ ${diffDays} يوم`;

        return lastSeenDate.toLocaleDateString('ar-EG');
    }

    // تنسيق رقم الهاتف للتنسيق العربي المصري
    formatPhoneNumber(phoneNumber) {
        if (!phoneNumber) return '';
        
        // إزالة المسافات والرموز
        const cleanNumber = phoneNumber.replace(/[\s\-\(\)]/g, '');

        // إضافة + إذا لم تكن موجودة
        const formattedNumber = cleanNumber.startsWith('+') ? cleanNumber : `+${cleanNumber}`;

        // إرجاع الرقم بدون مسافات (التنسيق العربي المصري)
        return formattedNumber;
    }

    // طلب رمز اقتران جديد
    async requestNewPairingCode(sessionId) {
        console.log(`Requesting new pairing code for session: ${sessionId}`);

        const account = this.accounts.get(sessionId);
        if (!account) {
            this.showNotification('الحساب غير موجود', 'error');
            return;
        }

        try {
            // إيقاف العد التنازلي الحالي إن وجد
            if (this.pairingCodeTimer) {
                clearInterval(this.pairingCodeTimer);
                this.pairingCodeTimer = null;
            }

            // إظهار مؤشر التحميل
            const loadingEl = document.getElementById('pairingLoading');
            const containerEl = document.getElementById('pairingCodeContainer');

            if (loadingEl && containerEl) {
                loadingEl.style.display = 'block';
                containerEl.style.display = 'none';
            }

            // طلب رمز جديد
            const response = await window.electronAPI.requestPairingCode({
                sessionId: sessionId,
                phoneNumber: account.phone
            });

            if (response.success) {
                console.log('New pairing code requested successfully');
                this.showNotification('تم طلب رمز اقتران جديد', 'success');
                // سيتم عرض الرمز الجديد تلقائياً عند استلامه من خلال حدث pairing-code-generated
            } else {
                console.error('Failed to request new pairing code:', response);
                this.showNotification(response.message || 'فشل في طلب رمز جديد', 'error');

                // إخفاء مؤشر التحميل في حالة الفشل
                if (loadingEl && containerEl) {
                    loadingEl.style.display = 'none';
                    containerEl.style.display = 'block';
                }
            }
        } catch (error) {
            console.error('Error requesting new pairing code:', error);
            this.showNotification('حدث خطأ أثناء طلب رمز جديد', 'error');
            
            // إخفاء مؤشر التحميل في حالة الخطأ
            const loadingEl = document.getElementById('pairingLoading');
            const containerEl = document.getElementById('pairingCodeContainer');
            if (loadingEl && containerEl) {
                loadingEl.style.display = 'none';
                containerEl.style.display = 'block';
            }
        }
    }

    // بدء العد التنازلي لرمز الاقتران
    startPairingCodeTimer() {
        // إيقاف أي عداد سابق
        if (this.pairingCodeTimer) {
            clearInterval(this.pairingCodeTimer);
        }

        // تحويل الرقم ٦٠ إلى رقم عربي
        let secondsLeft = 60;
        const timerElement = document.getElementById('codeTimer');
        
        if (!timerElement) return;
        
        // تحديث العداد كل ثانية
        this.pairingCodeTimer = setInterval(() => {
            secondsLeft--;
            
            // عرض الرقم
            timerElement.textContent = secondsLeft;
            
            // عند انتهاء الوقت
            if (secondsLeft <= 0) {
                clearInterval(this.pairingCodeTimer);
                timerElement.parentElement.innerHTML = '<p class="expired">انتهت صلاحية الرمز. يرجى طلب رمز جديد.</p>';
            }
        }, 1000);
    }
    


    // التحقق من صحة رقم الهاتف
    validatePhoneNumber(phoneNumber) {
        // إزالة المسافات والرموز والعلامة +
        let cleanNumber = phoneNumber.replace(/[\s\-\(\)\+]/g, '');
        
        // التحقق من تنسيق E.164 بدون علامة + (يبدأ برقم من 1-9 ويتكون من 10-15 رقم)
        // هذا يتوافق مع متطلبات Baileys: "The phone number MUST be in E.164 format without a plus sign"
        const phoneRegex = /^[1-9]\d{9,14}$/;
        
        return phoneRegex.test(cleanNumber);
    }

    // توليد معرف جلسة فريد
    generateSessionId(accountName, phoneNumber) {
        // إنشاء معرف فريد باستخدام عدة عوامل
        const timestamp = Date.now();
        const randomPart1 = Math.random().toString(36).substr(2, 4);
        const randomPart2 = Math.random().toString(36).substr(2, 4);

        // تنظيف اسم الحساب
        const safeName = accountName.replace(/[^a-zA-Z0-9]/g, '').toLowerCase().substr(0, 8);

        // استخدام آخر 4 أرقام من رقم الهاتف
        const phoneHash = phoneNumber.slice(-4);

        // إنشاء hash بسيط من البيانات
        const dataString = `${accountName}${phoneNumber}${timestamp}`;
        let hash = 0;
        for (let i = 0; i < dataString.length; i++) {
            const char = dataString.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        const hashString = Math.abs(hash).toString(36).substr(0, 4);

        // التأكد من عدم وجود معرف مشابه
        let sessionId = `${safeName}_${phoneHash}_${hashString}_${randomPart1}${randomPart2}`;
        let counter = 1;

        while (this.accounts.has(sessionId)) {
            sessionId = `${safeName}_${phoneHash}_${hashString}_${randomPart1}${randomPart2}_${counter}`;
            counter++;
        }

        return sessionId;
    }

    // عرض إشعار
    showNotification(message, type = 'info') {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;
        
        // إضافة الإشعار إلى الصفحة
        document.body.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // إخفاء الإشعار تلقائياً
        setTimeout(() => {
            this.hideNotification(notification);
        }, 5000);
        
        // إضافة مستمع لزر الإغلاق
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.hideNotification(notification);
        });
    }

    // إخفاء الإشعار
    hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // فتح المحادثة
    openChat(sessionId) {
        // يمكن تطوير هذه الوظيفة لاحقاً
        this.showNotification('ميزة المحادثة قيد التطوير', 'info');
    }

    // عرض تفاصيل الحساب
    viewAccountDetails(sessionId) {
        const account = this.accounts.get(sessionId);
        if (account) {
            // التوجه إلى صفحة تفاصيل الحساب
            window.location.href = `account-details.html?sessionId=${encodeURIComponent(sessionId)}`;
        } else {
            this.showNotification('لم يتم العثور على الحساب', 'error');
        }
    }

    // إدارة حساب
    manageAccount(sessionId) {
        const account = this.accounts.get(sessionId);
        
        if (!account) {
            this.showNotification('لم يتم العثور على الحساب', 'error');
            return;
        }
        
        // يمكن إضافة نافذة إدارة الحساب هنا
        this.showNotification('ميزة إدارة الحساب قيد التطوير', 'info');
    }

    // الحصول على نص آخر ظهور
    getLastSeenText(lastSeen) {
        if (!lastSeen) return 'غير محدد';
        
        const now = new Date();
        const lastSeenDate = new Date(lastSeen);
        const diffMs = now - lastSeenDate;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / ********);
        
        if (diffMins < 1) return 'الآن';
        if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
        if (diffHours < 24) return `منذ ${diffHours} ساعة`;
        if (diffDays < 7) return `منذ ${diffDays} يوم`;
        
        return lastSeenDate.toLocaleDateString('ar-SA');
    }

    // معالجة الجلسات التي تحتاج إعادة مصادقة
    onSessionAuthRequired(sessionId, userInfo, message, canReconnect) {
        console.log(`Session ${sessionId} requires authentication`);
        
        // تحديث حالة الحساب
        const account = this.accounts.get(sessionId);
        if (account) {
            account.status = 'auth_required';
            account.canReconnect = canReconnect;
            this.updateAccountsDisplay();
        }
        
        // عرض إشعار
        this.showNotification(message, 'warning');
        
        // تحديث كارت الحساب لإظهار خيار إعادة الاتصال
        this.updateAccountCard(sessionId, {
            status: 'auth_required',
            message: message,
            canReconnect: canReconnect
        });
    }

    // معالجة بدء الاتصال بالجلسة
    onSessionConnecting(sessionId, userInfo, message) {
        console.log(`Session ${sessionId} is connecting`);
        
        // تحديث حالة الحساب
        const account = this.accounts.get(sessionId);
        if (account) {
            account.status = 'connecting';
            this.updateAccountsDisplay();
        }
        
        // عرض إشعار
        this.showNotification(message, 'info');
        
        // تحديث كارت الحساب
        this.updateAccountCard(sessionId, {
            status: 'connecting',
            message: message
        });
    }

    // معالجة انقطاع الاتصال بالجلسة
    onSessionDisconnected(sessionId, userInfo, message) {
        console.log(`Session ${sessionId} disconnected`);
        
        // تحديث حالة الحساب
        const account = this.accounts.get(sessionId);
        if (account) {
            account.status = 'disconnected';
            this.updateAccountsDisplay();
        }
        
        // عرض إشعار
        this.showNotification(message, 'warning');
        
        // تحديث كارت الحساب
        this.updateAccountCard(sessionId, {
            status: 'disconnected',
            message: message
        });
    }

    // تحديث كارت الحساب
    updateAccountCard(sessionId, statusInfo) {
        // منع التحديث المتكرر للكارت نفسه
        const cardUpdateKey = `card_${sessionId}`;
        if (this.cardUpdating && this.cardUpdating[cardUpdateKey]) {
            return;
        }
        
        if (!this.cardUpdating) {
            this.cardUpdating = {};
        }
        this.cardUpdating[cardUpdateKey] = true;
        
        const accountCard = document.querySelector(`[data-account-id="${sessionId}"]`);
        if (!accountCard) {
            this.cardUpdating[cardUpdateKey] = false;
            return;
        }
        
        const statusElement = accountCard.querySelector('.account-status');
        const actionsElement = accountCard.querySelector('.account-actions');
        
        if (statusElement) {
            // تحديث نص الحالة
            let statusText = '';
            let statusClass = '';
            
            switch (statusInfo.status) {
                case 'connecting':
                    statusText = 'جاري الاتصال...';
                    statusClass = 'status-connecting';
                    break;
                case 'auth_required':
                    statusText = 'يحتاج إعادة مصادقة';
                    statusClass = 'status-auth-required';
                    break;
                case 'disconnected':
                    statusText = 'منقطع الاتصال';
                    statusClass = 'status-disconnected';
                    break;
                case 'ready':
                    statusText = 'متصل';
                    statusClass = 'status-connected';
                    break;
                default:
                    statusText = statusInfo.status;
                    statusClass = 'status-unknown';
            }
            
            statusElement.textContent = statusText;
            statusElement.className = `account-status ${statusClass}`;
        }
        
        // إضافة زر إعادة الاتصال إذا كان متاحاً
        if (statusInfo.canReconnect && actionsElement) {
            const reconnectBtn = document.createElement('button');
            reconnectBtn.className = 'btn btn-primary btn-sm';
            reconnectBtn.textContent = 'إعادة الاتصال';
            reconnectBtn.onclick = () => this.reconnectAccount(sessionId);
            
            // إزالة أي زر إعادة اتصال سابق
            const existingBtn = actionsElement.querySelector('.btn-primary');
            if (existingBtn) {
                existingBtn.remove();
            }
            
            actionsElement.insertBefore(reconnectBtn, actionsElement.firstChild);
        }
        
        // إنهاء عملية تحديث الكارت
        setTimeout(() => {
            if (this.cardUpdating) {
                this.cardUpdating[cardUpdateKey] = false;
            }
        }, 100);
    }

    // إعادة الاتصال بالحساب
    async reconnectAccount(sessionId) {
        try {
            this.showNotification('جاري محاولة إعادة الاتصال...', 'info');
            
            if (window.electronAPI) {
                const response = await window.electronAPI.reconnectSession(sessionId);
                
                if (response.success) {
                    this.showNotification('تم بدء إعادة الاتصال بنجاح', 'success');
                } else {
                    this.showNotification(response.message || 'فشل في إعادة الاتصال', 'error');
                }
            }
        } catch (error) {
            console.error('Error reconnecting account:', error);
            this.showNotification('حدث خطأ أثناء إعادة الاتصال', 'error');
        }
    }
}

// إنشاء مثيل من مدير WhatsApp
document.addEventListener('DOMContentLoaded', () => {
    whatsappManager = new WhatsAppManager();
    window.whatsappManager = whatsappManager;
});