// preload.js
const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
  // WhatsApp APIs
  createWhatsAppSession: (data) => ipcRenderer.invoke('whatsapp-create-session', data),
  requestPairingCode: (data) => ipcRenderer.invoke('whatsapp-request-pairing-code', data),
  reconnectSession: (sessionId) => ipcRenderer.invoke('whatsapp-reconnect-session', sessionId),
  getSessionStatus: (sessionId) => ipcRenderer.invoke('whatsapp-get-session-status', sessionId),
  deleteSession: (sessionId) => ipcRenderer.invoke('whatsapp-delete-session', sessionId),
  forceDeleteAccount: (sessionId) => ipcRenderer.invoke('whatsapp-force-delete-account', sessionId),
  getAllSessions: () => ipcRenderer.invoke('whatsapp-get-all-sessions'),
  sendMessage: (data) => ipcRenderer.invoke('whatsapp-send-message', data),
  getContacts: (sessionId, useCache = true) => ipcRenderer.invoke('whatsapp-get-contacts', sessionId, useCache),

  // تفعيل وظائف WhatsApp بعد تسجيل الدخول
  enableWhatsAppFunctions: () => ipcRenderer.invoke('enable-whatsapp-functions'),
  
  // الاستماع لتحديثات جهات الاتصال التدريجية
  onContactsProgress: (callback) => {
    ipcRenderer.on('contacts-progress', (event, data) => callback(data));
  },
  
  // إزالة مستمع تحديثات جهات الاتصال
  removeContactsProgressListener: () => {
    ipcRenderer.removeAllListeners('contacts-progress');
  },
  
  // Event listeners
  onQRCodeGenerated: (callback) => ipcRenderer.on('qr-code-generated', callback),
  onPairingCodeGenerated: (callback) => ipcRenderer.on('pairing-code-generated', callback),
  onSessionConnected: (callback) => ipcRenderer.on('session-connected', callback),
  onSessionDisconnected: (callback) => ipcRenderer.on('session-disconnected', callback),
  onMessageReceived: (callback) => ipcRenderer.on('message-received', callback),
  onSessionsLoaded: (callback) => ipcRenderer.on('sessions-loaded', callback),
  onSessionReconnecting: (callback) => ipcRenderer.on('session-reconnecting', callback),
  onSessionConnectionFailed: (callback) => ipcRenderer.on('session-connection-failed', callback),
  onSessionAuthRequired: (callback) => ipcRenderer.on('session-auth-required', callback),
  onSessionConnecting: (callback) => ipcRenderer.on('session-connecting', callback),
  onStatsUpdated: (callback) => ipcRenderer.on('stats-updated', callback),

  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});