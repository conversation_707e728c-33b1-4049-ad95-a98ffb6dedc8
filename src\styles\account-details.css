/* تنسيق صفحة تفاصيل الحساب */

/* تحسين شريط التنقل ليتضمن معلومات الحساب */
.app-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    gap: 1rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.header-left h1 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: white;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    text-decoration: none;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

/* معلومات الحساب المدمجة */
.account-header-compact {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    min-width: 0;
}

.account-avatar-small {
    flex-shrink: 0;
}

.avatar-placeholder-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.avatar-placeholder-small svg {
    color: rgba(255, 255, 255, 0.8);
}

.account-info-compact {
    flex: 1;
    min-width: 0;
}

.account-name-small {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.account-phone-small {
    font-size: 0.8rem;
    margin: 0 0 0.25rem 0;
    color: rgba(255, 255, 255, 0.8);
    font-family: 'Courier New', monospace;
}

.account-status-small {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.status-indicator-small {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ade80;
    box-shadow: 0 0 4px rgba(74, 222, 128, 0.5);
}

.status-indicator-small.status-connecting {
    background: #fbbf24;
    animation: pulse 2s infinite;
}

.status-indicator-small.status-disconnected {
    background: #ef4444;
}

.status-text-small {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.account-stats-compact {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.stat-item-small {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-number-small {
    font-size: 1rem;
    font-weight: 700;
    color: white;
    line-height: 1;
}

.stat-label-small {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 0.125rem;
}

/* تحسين المحتوى الرئيسي */
.main-content {
    margin-top: 80px; /* مساحة لشريط التنقل الثابت */
    padding: 1.5rem;
    height: calc(100vh - 80px);
    overflow-y: auto;
}

/* تنسيق الجدول الجديد برأس ثابت */
.table-wrapper {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.table-header-fixed {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #f8fafc;
    border-bottom: 2px solid #e5e7eb;
}

.contacts-table-header {
    width: 100%;
    border-collapse: collapse;
}

.contacts-table-header th {
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: #374151;
    background: #f8fafc;
    border: none;
    font-size: 0.9rem;
}

.table-body-scrollable {
    max-height: calc(100vh - 300px); /* ارتفاع قابل للتمرير */
    overflow-y: auto;
    overflow-x: hidden;
}

.contacts-table-body {
    width: 100%;
    border-collapse: collapse;
}

.contacts-table-body td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
    font-size: 0.9rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.contacts-table-body tr:hover {
    background: #f8fafc;
}

.contacts-table-body tr:last-child td {
    border-bottom: none;
}

/* تحديد عرض الأعمدة - تزامن بين الرأس والجسم */
.col-avatar,
.contacts-table-body td:nth-child(1) {
    width: 80px;
    min-width: 80px;
    max-width: 80px;
    text-align: center;
}

.col-name,
.contacts-table-body td:nth-child(2) {
    width: 35%;
    min-width: 200px;
}

.col-phone,
.contacts-table-body td:nth-child(3) {
    width: 25%;
    min-width: 150px;
}

.col-whatsapp,
.contacts-table-body td:nth-child(4) {
    width: 20%;
    min-width: 120px;
}

/* ضمان تطابق عرض الجداول */
.contacts-table-header,
.contacts-table-body {
    table-layout: fixed;
}

/* تحسين أنماط جهات الاتصال */
.contact-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
}

.contact-avatar-placeholder {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #e5e7eb;
}

.contact-avatar-placeholder svg {
    color: #9ca3af;
    width: 16px;
    height: 16px;
}

.contact-name-cell {
    font-weight: 500;
    color: #1f2937;
}

.contact-name-primary {
    display: block;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.125rem;
}

.contact-name-secondary {
    display: block;
    font-size: 0.8rem;
    color: #6b7280;
    font-style: italic;
}

.contact-saved-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #059669;
    margin-top: 0.125rem;
}

.contact-saved-indicator svg {
    width: 12px;
    height: 12px;
}

.contact-phone {
    font-family: 'Courier New', monospace;
    color: #4b5563;
    font-size: 0.9rem;
}

.contact-whatsapp {
    color: #6b7280;
    font-size: 0.85rem;
}

/* تنسيق محتوى جهات الاتصال */
.contact-name-container {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.contact-saved-name {
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.saved-indicator {
    font-size: 0.75rem;
}

.contact-whatsapp-name {
    font-size: 0.8rem;
    color: #6b7280;
    font-style: italic;
}

.contact-verified-name {
    font-size: 0.75rem;
    color: #059669;
    font-weight: 500;
}

.whatsapp-status {
    text-align: center;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.has-whatsapp {
    background: #dcfce7;
    color: #166534;
}

.status-badge.no-whatsapp {
    background: #fef2f2;
    color: #991b1b;
}

.status-icon {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
}

.has-whatsapp .status-icon {
    background: #22c55e;
}

.no-whatsapp .status-icon {
    background: #ef4444;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.25rem;
    display: block;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* قسم جهات الاتصال */
.contacts-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.section-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f9fafb;
}

.section-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.contacts-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    width: 250px;
    transition: all 0.2s;
}

.search-box input:focus {
    outline: none;
    border-color: #25d366;
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
}

.search-box svg {
    position: absolute;
    left: 0.75rem;
    color: #6b7280;
    pointer-events: none;
}

.refresh-buttons {
    display: flex;
    gap: 8px;
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: #25d366;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.refresh-btn:hover {
    background: #22c55e;
    transform: translateY(-1px);
}

.refresh-btn.force-refresh {
    background: #f59e0b;
}

.refresh-btn.force-refresh:hover {
    background: #d97706;
}

.refresh-btn i {
    font-size: 12px;
}

.refresh-btn span {
    font-size: 12px;
}

.refresh-btn:active {
    transform: translateY(0);
}

/* جدول جهات الاتصال */
.contacts-table-container {
    min-height: 400px;
    position: relative;
}

.contacts-table {
    width: 100%;
    border-collapse: collapse;
}

.contacts-table th {
    background: #f9fafb;
    padding: 1rem 1.5rem;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    font-size: 0.875rem;
}

.contacts-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
}

.contacts-table tbody tr {
    transition: background-color 0.2s;
}

.contacts-table tbody tr:hover {
    background: #f9fafb;
}

/* خلية صورة جهة الاتصال */
.contact-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
}

.contact-avatar-placeholder {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #e5e7eb;
}

.contact-avatar-placeholder svg {
    color: #9ca3af;
    width: 24px;
    height: 24px;
}

/* خلية الاسم */
.contact-name-cell {
    min-width: 200px;
}

.contact-saved-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-whatsapp-name {
    font-size: 0.8rem;
    color: #6b7280;
    font-style: italic;
}

.contact-verified-name {
    font-size: 0.75rem;
    color: #059669;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-top: 0.125rem;
}

.saved-indicator {
    font-size: 0.75rem;
    color: #059669;
    background: #ecfdf5;
    padding: 0.125rem 0.25rem;
    border-radius: 4px;
    border: 1px solid #a7f3d0;
}

.verified-icon {
    color: #059669;
    font-size: 0.75rem;
}

/* خلية رقم الهاتف */
.contact-phone {
    font-family: 'Courier New', monospace;
    color: #374151;
    font-weight: 500;
    direction: ltr;
    text-align: left;
}

/* خلية حالة WhatsApp */
.whatsapp-status {
    text-align: center;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.has-whatsapp {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-badge.no-whatsapp {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.status-badge .status-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-badge.has-whatsapp .status-icon {
    background: #22c55e;
}

.status-badge.no-whatsapp .status-icon {
    background: #ef4444;
}

/* حالات التحميل والفراغ */
.loading-state,
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
}

.loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #25d366;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.loading-message {
    font-size: 14px;
    color: #6b7280;
}

.progress-container {
    width: 200px;
    height: 4px;
    background-color: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #25d366, #128c7e);
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
}

.empty-state .empty-icon {
    margin-bottom: 1rem;
    color: #9ca3af;
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: #374151;
    font-size: 1.25rem;
}

.empty-state p {
    margin: 0;
    color: #6b7280;
    font-size: 0.95rem;
}

/* زر العودة */
.back-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: 1rem;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.back-btn:active {
    transform: translateY(0);
}

/* الرسوم المتحركة */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .account-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .account-stats-large {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .contacts-controls {
        flex-direction: column;
        gap: 0.75rem;
        width: 100%;
    }
    
    .search-box input {
        width: 100%;
    }
    
    .contacts-table {
        font-size: 0.8rem;
    }
    
    .contacts-table th,
    .contacts-table td {
        padding: 0.75rem 0.5rem;
    }
    
    .contact-name-cell {
        min-width: 150px;
    }
}

@media (max-width: 480px) {
    .account-header {
        padding: 1.5rem;
    }
    
    .account-avatar-large .avatar-image,
    .account-avatar-large .avatar-placeholder {
        width: 80px;
        height: 80px;
    }
    
    .account-info-large .account-name {
        font-size: 1.5rem;
    }
    
    .stat-card {
        padding: 1rem 0.75rem;
        min-width: 80px;
    }
    
    .stat-card .stat-number {
        font-size: 1.5rem;
    }
    
    .section-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .contacts-table th:first-child,
    .contacts-table td:first-child {
        display: none; /* إخفاء عمود الصورة في الشاشات الصغيرة جداً */
    }
}

/* تنسيق نظام التنقل بين الصفحات */
.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.items-per-page {
    padding: 0.5rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
}

.items-per-page:hover {
    border-color: #25d366;
}

.items-per-page:focus {
    outline: none;
    border-color: #25d366;
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    border-top: 1px solid #e5e7eb;
    margin-top: 1rem;
}

.pagination-info {
    color: #6b7280;
    font-size: 0.9rem;
}

.pagination-controls-bottom {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #374151;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #f9fafb;
    border-color: #25d366;
    color: #25d366;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f9fafb;
}

.page-numbers {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin: 0 0.5rem;
}

.page-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    color: #374151;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-number:hover {
    background: #f9fafb;
    border-color: #25d366;
    color: #25d366;
}

.page-number.active {
    background: #25d366;
    border-color: #25d366;
    color: white;
}

.page-ellipsis {
    padding: 0 0.5rem;
    color: #9ca3af;
}

/* تحسين تخطيط عناصر التحكم */
.contacts-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.contacts-controls .search-box {
    flex: 1;
    min-width: 250px;
}

.contacts-controls .pagination-controls {
    flex-shrink: 0;
}

.contacts-controls .refresh-buttons {
    flex-shrink: 0;
}

/* تنسيق نظام الإشعارات */
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    max-width: 400px;
    min-width: 300px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    z-index: 1000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    overflow: hidden;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left: 4px solid #10b981;
}

.notification.error {
    border-left: 4px solid #ef4444;
}

.notification.info {
    border-left: 4px solid #3b82f6;
}

.notification.warning {
    border-left: 4px solid #f59e0b;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.25rem;
    gap: 1rem;
}

.notification-message {
    flex: 1;
    font-size: 0.9rem;
    color: #374151;
    line-height: 1.4;
}

.notification-close {
    flex-shrink: 0;
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    background: #f3f4f6;
    color: #6b7280;
}

.notification-close svg {
    width: 16px;
    height: 16px;
}

/* تحسين الإشعارات للشاشات الصغيرة */
@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        min-width: auto;
    }
}