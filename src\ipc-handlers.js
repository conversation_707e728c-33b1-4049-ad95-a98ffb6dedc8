const { ipcMain } = require('electron');

/**
 * إعداد معالجات IPC للتواصل مع WhatsApp Manager
 */
function setupIPCHandlers(whatsappManager) {
    console.log('Setting up IPC handlers for WhatsApp Web.js');

    // إنشاء جلسة WhatsApp جديدة
    ipcMain.handle('whatsapp-create-session', async (event, data) => {
        try {
            console.log('IPC: Creating WhatsApp session:', data);
            const { sessionId, connectionType, phoneNumber } = data;
            
            const result = await whatsappManager.createSession(sessionId, connectionType, phoneNumber);
            console.log('IPC: Session creation result:', result);
            
            return result;
        } catch (error) {
            console.error('IPC: Error creating session:', error);
            return { success: false, error: error.message };
        }
    });

    // طلب رمز الاقتران (يتم تلقائياً عند إنشاء جلسة pairing)
    ipcMain.handle('whatsapp-request-pairing-code', async (event, data) => {
        try {
            console.log('IPC: Pairing code request:', data);

            // مع whatsapp-web.js المحدثة، رمز الاقتران يتم تلقائياً عند إنشاء الجلسة
            return {
                success: true,
                message: 'رمز الاقتران سيتم طلبه تلقائياً عند إنشاء الجلسة'
            };
        } catch (error) {
            console.error('IPC: Error in pairing code request:', error);
            return { success: false, error: error.message };
        }
    });

    // الحصول على حالة الجلسة
    ipcMain.handle('whatsapp-get-session-status', async (event, sessionId) => {
        try {
            console.log('IPC: Getting session status:', sessionId);
            
            const sessions = whatsappManager.getAllSessions();
            const session = sessions.find(s => s.id === sessionId);
            
            if (session) {
                return { success: true, status: session.status, session };
            } else {
                return { success: false, error: 'Session not found' };
            }
        } catch (error) {
            console.error('IPC: Error getting session status:', error);
            return { success: false, error: error.message };
        }
    });

    // حذف جلسة
    ipcMain.handle('whatsapp-delete-session', async (event, sessionId) => {
        try {
            console.log('IPC: Deleting session:', sessionId);
            
            const result = await whatsappManager.deleteSession(sessionId);
            console.log('IPC: Session deletion result:', result);
            
            return result;
        } catch (error) {
            console.error('IPC: Error deleting session:', error);
            return { success: false, error: error.message };
        }
    });

    // حذف حساب بالكامل (تجاوز جميع الحمايات)
    ipcMain.handle('whatsapp-force-delete-account', async (event, sessionId) => {
        try {
            console.log('IPC: Force deleting account:', sessionId);
            
            const result = await whatsappManager.forceDeleteAccount(sessionId);
            console.log('IPC: Force account deletion result:', result);
            
            return result;
        } catch (error) {
            console.error('IPC: Error force deleting account:', error);
            return { success: false, error: error.message };
        }
    });

    // الحصول على جميع الجلسات
    ipcMain.handle('whatsapp-get-all-sessions', async (event) => {
        try {
            console.log('IPC: Getting all sessions');
            
            const sessions = whatsappManager.getAllSessions();
            console.log('IPC: Found sessions:', sessions.length);
            
            return { success: true, sessions };
        } catch (error) {
            console.error('IPC: Error getting all sessions:', error);
            return { success: false, error: error.message };
        }
    });

    // إرسال رسالة
    ipcMain.handle('whatsapp-send-message', async (event, data) => {
        try {
            console.log('IPC: Sending message:', data);
            const { sessionId, to, message } = data;
            
            const result = await whatsappManager.sendMessage(sessionId, to, message);
            console.log('IPC: Message send result:', result);
            
            return result;
        } catch (error) {
            console.error('IPC: Error sending message:', error);
            return { success: false, error: error.message };
        }
    });

    // إرسال ملف
    ipcMain.handle('whatsapp-send-media', async (event, data) => {
        try {
            console.log('IPC: Sending media:', data);
            const { sessionId, to, mediaPath, caption } = data;
            
            const result = await whatsappManager.sendMedia(sessionId, to, mediaPath, caption);
            console.log('IPC: Media send result:', result);
            
            return result;
        } catch (error) {
            console.error('IPC: Error sending media:', error);
            return { success: false, error: error.message };
        }
    });

    // الحصول على معلومات جهة الاتصال
    ipcMain.handle('whatsapp-get-contact-info', async (event, data) => {
        try {
            console.log('IPC: Getting contact info:', data);
            const { sessionId, contactId } = data;
            
            const result = await whatsappManager.getContactInfo(sessionId, contactId);
            console.log('IPC: Contact info result:', result);
            
            return result;
        } catch (error) {
            console.error('IPC: Error getting contact info:', error);
            return { success: false, error: error.message };
        }
    });

    // معالج لتفعيل وظائف WhatsApp بعد تسجيل الدخول
    ipcMain.handle('enable-whatsapp-functions', async (event) => {
        try {
            console.log('IPC: Enabling WhatsApp functions after login');
            whatsappManager.enableFunctionsAfterLogin();
            return { success: true };
        } catch (error) {
            console.error('IPC: Error enabling WhatsApp functions:', error);
            return { success: false, error: error.message };
        }
    });

    // معالج للحصول على جهات الاتصال مع التحديث التدريجي
    ipcMain.handle('whatsapp-get-contacts', async (event, sessionId, useCache = true) => {
        try {
            console.log(`IPC: Getting contacts for session ${sessionId}, useCache: ${useCache}`);
            
            // إنشاء دالة callback لإرسال التحديثات التدريجية
            const progressCallback = (progressData) => {
                event.sender.send('contacts-progress', {
                    sessionId,
                    ...progressData
                });
            };
            
            const result = await whatsappManager.getContacts(sessionId, useCache, progressCallback);
            console.log(`IPC: Retrieved contacts result:`, { 
                success: result.success, 
                contactCount: result.contacts?.length || 0,
                fromCache: result.fromCache || false
            });
            
            return result;
        } catch (error) {
            console.error('IPC: Error getting contacts:', error);
            return { success: false, error: error.message };
        }
    });

    // إعادة الاتصال بجلسة محفوظة
    ipcMain.handle('whatsapp-reconnect-session', async (event, sessionId) => {
        try {
            console.log('IPC: Reconnecting to session:', sessionId);

            const result = await whatsappManager.reconnectSession(sessionId);
            console.log('IPC: Reconnection result:', result);

            return result;
        } catch (error) {
            console.error('IPC: Error reconnecting session:', error);
            return { success: false, error: error.message };
        }
    });

    console.log('IPC handlers setup complete');
}

module.exports = { setupIPCHandlers };
