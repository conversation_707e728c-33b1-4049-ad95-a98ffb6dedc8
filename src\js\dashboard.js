// استخدام Firebase من المتغيرات العامة
// سيتم تحميل Firebase من firebase-config.js

// متغيرات عامة
const userNameElement = document.getElementById('userName');
const userEmailElement = document.getElementById('userEmail');
const logoutBtn = document.getElementById('logoutBtn');
const featureBtns = document.querySelectorAll('.feature-btn');
const statNumbers = document.querySelectorAll('.stat-number');

// دالة لإظهار رسائل التنبيه
function showAlert(message, type = 'info') {
  const alert = document.createElement('div');
  alert.className = `alert alert-${type}`;
  alert.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  `;
  
  const colors = {
    success: '#28a745',
    error: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8'
  };
  
  alert.style.backgroundColor = colors[type] || colors.info;
  alert.textContent = message;
  
  document.body.appendChild(alert);
  
  setTimeout(() => {
    alert.style.animation = 'slideOut 0.3s ease-in';
    setTimeout(() => {
      if (alert.parentNode) {
        alert.parentNode.removeChild(alert);
      }
    }, 300);
  }, 5000);
}

// دالة لتحديث معلومات المستخدم من الجلسة المحفوظة
function updateUserInfo(userSession) {
  if (userSession) {
    // تحديث اسم المستخدم
    const displayName = userSession.displayName || userSession.username || 'مستخدم';
    userNameElement.textContent = `مرحباً ${displayName}`;
    
    // تحديث البريد الإلكتروني
    userEmailElement.textContent = userSession.email || 'لا يوجد بريد إلكتروني';
    
    // عرض معلومات تاريخ الصلاحية في رأس الصفحة
    displayExpiryInfoInHeader(userSession.expiryDate);
    
    console.log('✅ تم تحديث معلومات المستخدم:', {
      name: displayName,
      email: userSession.email,
      username: userSession.username
    });
  }
}

// دالة تسجيل الخروج
function logout() {
  try {
    // إيقاف الفحص الدوري إذا كان يعمل
    if (expiryCheckIntervalId) {
      clearInterval(expiryCheckIntervalId);
      console.log('🛑 تم إيقاف الفحص الدوري للصلاحية');
    }

    // إظهار مؤشر التحميل
    logoutBtn.disabled = true;
    logoutBtn.innerHTML = `
      <div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3); border-radius: 50%; border-top-color: #fff; animation: spin 1s ease-in-out infinite;"></div>
      جاري تسجيل الخروج...
    `;
    
    // مسح البيانات المحفوظة محلياً
    localStorage.removeItem('userSession');
    localStorage.removeItem('rememberUser');
    localStorage.removeItem('savedUsername');
    
    console.log('✅ تم تسجيل الخروج بنجاح');
    showAlert('تم تسجيل الخروج بنجاح', 'success');
    
    // الانتقال إلى صفحة تسجيل الدخول بعد ثانيتين
    setTimeout(() => {
      window.location.href = 'login.html';
    }, 2000);
    
  } catch (error) {
    console.error('❌ خطأ في تسجيل الخروج:', error);
    showAlert('حدث خطأ في تسجيل الخروج', 'error');
    
    // إعادة تعيين زر تسجيل الخروج
    logoutBtn.disabled = false;
    logoutBtn.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4M16 17l5-5-5-5M21 12H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      تسجيل الخروج
    `;
  }
}



// دالة لمعالجة النقر على أزرار المميزات
function handleFeatureClick(featureName) {
  const messages = {
    'فتح الرسائل': 'ميزة الرسائل ستكون متاحة قريباً',
    'عرض جهات الاتصال': 'ميزة جهات الاتصال ستكون متاحة قريباً',
    'استكشاف المميزات': 'المميزات الخاصة ستكون متاحة قريباً',
    'فتح الإعدادات': 'صفحة الإعدادات ستكون متاحة قريباً'
  };
  
  const message = messages[featureName] || 'هذه الميزة ستكون متاحة قريباً';
  showAlert(message, 'info');
}

// دالة لتحديث الوقت الحالي
function updateCurrentTime() {
  const now = new Date();
  const options = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  
  const arabicTime = now.toLocaleDateString('ar-EG', options);
  
  // إضافة عنصر الوقت إذا لم يكن موجوداً
  let timeElement = document.getElementById('currentTime');
  if (!timeElement) {
    timeElement = document.createElement('div');
    timeElement.id = 'currentTime';
    timeElement.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 20px;
      background: rgba(255, 255, 255, 0.9);
      padding: 10px 15px;
      border-radius: 8px;
      font-size: 14px;
      color: #666;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.2);
    `;
    document.body.appendChild(timeElement);
  }
  
  timeElement.textContent = arabicTime;
}

// دالة لعرض معلومات تاريخ الصلاحية في رأس الصفحة


function displayExpiryInfoInHeader(expiryDate) {
  const expiryCheck = checkExpiryDate(expiryDate);
  
  // البحث عن قسم معلومات المستخدم في الرأس
  const userSection = document.querySelector('.user-section .user-info');
  if (!userSection) {
    console.error('❌ لم يتم العثور على قسم معلومات المستخدم في الرأس');
    return;
  }
  
  // البحث عن عنصر معلومات الصلاحية أو إنشاؤه
  let expiryInfoElement = document.getElementById('expiryInfo');
  if (!expiryInfoElement) {
    expiryInfoElement = document.createElement('span');
    expiryInfoElement.id = 'expiryInfo';
    expiryInfoElement.className = 'user-expiry';
    userSection.appendChild(expiryInfoElement);
  }
  
  // التحقق من صحة البيانات قبل العرض
  if (!expiryCheck.isValid || expiryCheck.daysRemaining === null || isNaN(expiryCheck.daysRemaining)) {
    expiryInfoElement.innerHTML = `❌ ${expiryCheck.message || 'خطأ في بيانات الصلاحية'}`;
    expiryInfoElement.style.color = '#dc3545';
    expiryInfoElement.title = `حالة الصلاحية: ${expiryCheck.expiryDateFormatted || 'غير محدد'}`;
    
    console.error('❌ خطأ في عرض معلومات الصلاحية:', {
      isValid: expiryCheck.isValid,
      daysRemaining: expiryCheck.daysRemaining,
      expiryDate: expiryCheck.expiryDateFormatted,
      message: expiryCheck.message
    });
    return;
  }
  
  // تحديث محتوى العنصر مع التحقق من صحة القيم
  let daysText;
  const days = expiryCheck.daysRemaining;
  
  if (days === 1) {
    daysText = 'يوم واحد';
  } else if (days === 2) {
    daysText = 'يومان';
  } else if (days <= 10) {
    daysText = `${days} أيام`;
  } else {
    daysText = `${days} يوماً`;
  }
  
  // تحديد اللون والرمز بناءً على عدد الأيام المتبقية
  if (days <= 3) {
    expiryInfoElement.innerHTML = `🔴 متبقي: ${daysText}`;
    expiryInfoElement.style.color = '#dc3545'; // أحمر
  } else if (days <= 7) {
    expiryInfoElement.innerHTML = `🟠 متبقي: ${daysText}`;
    expiryInfoElement.style.color = '#ff6b35'; // برتقالي
  } else {
    expiryInfoElement.innerHTML = `🟢 متبقي: ${daysText}`;
    expiryInfoElement.style.color = '#28a745'; // أخضر
  }
  
  // إضافة تلميح يظهر التاريخ الكامل عند التمرير
  expiryInfoElement.title = `تاريخ انتهاء الصلاحية: ${expiryCheck.expiryDateFormatted}`;
  
  console.log('📅 معلومات الصلاحية:', {
    isValid: expiryCheck.isValid,
    daysRemaining: expiryCheck.daysRemaining,
    expiryDate: expiryCheck.expiryDateFormatted,
    message: expiryCheck.message
  });
}

// دالة لإضافة تأثيرات بصرية
function addVisualEffects() {
  // إضافة تأثير التمرير السلس
  document.documentElement.style.scrollBehavior = 'smooth';
  
  // إضافة تأثير الظهور التدريجي للعناصر
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);
  
  // مراقبة العناصر القابلة للرسوم المتحركة
  document.querySelectorAll('.feature-card, .stat-card').forEach(el => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(20px)';
    el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(el);
  });
}

// معالج أحداث تسجيل الخروج
logoutBtn.addEventListener('click', logout);

// معالج أحداث أزرار المميزات
featureBtns.forEach(btn => {
  btn.addEventListener('click', () => {
    const featureName = btn.textContent.trim();
    handleFeatureClick(featureName);
  });
});

// متغير لتخزين معرف المؤقت
let expiryCheckIntervalId = null;

// دالة لتعطيل جميع الميزات وتغطية الواجهة
function disableAllFeatures() {
  console.warn('⏳ تعطيل جميع الميزات بسبب انتهاء صلاحية الجلسة');
  
  // تعطيل جميع الأزرار والروابط باستثناء عناصر النوافذ المنبثقة
  document.querySelectorAll('button, a').forEach(el => {
    // استثناء عناصر النوافذ المنبثقة وحقول النماذج
    const isModalElement = el.closest('.modal-overlay') || el.closest('.modal-content');
    const isFormElement = el.closest('form') || el.tagName === 'INPUT' || el.tagName === 'TEXTAREA' || el.tagName === 'SELECT';
    
    if (!isModalElement && !isFormElement) {
      el.disabled = true;
      el.style.pointerEvents = 'none';
      el.style.opacity = '0.5';
    }
  });
  
  // تعطيل حقول الإدخال باستثناء تلك الموجودة في النوافذ المنبثقة
  document.querySelectorAll('input, textarea, select').forEach(el => {
    const isModalElement = el.closest('.modal-overlay') || el.closest('.modal-content');
    
    if (!isModalElement) {
      el.disabled = true;
      el.style.pointerEvents = 'none';
      el.style.opacity = '0.5';
    }
  });

  // إظهار طبقة تغطية
  let overlay = document.getElementById('session-expired-overlay');
  if (!overlay) {
    overlay = document.createElement('div');
    overlay.id = 'session-expired-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      z-index: 2000;
      text-align: center;
      backdrop-filter: blur(5px);
    `;
    overlay.innerHTML = '<div>انتهت صلاحية جلستك.<br>سيتم تسجيل خروجك الآن.</div>';
    document.body.appendChild(overlay);
  }
}

// دالة لبدء الفحص الدوري لتاريخ انتهاء الصلاحية
// دالة للتحقق من بيانات المستخدم من قاعدة البيانات مباشرة
async function fetchUserDataFromDatabase(userId) {
  try {
    const db = window.firebaseDb || firebase.firestore();
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      console.error('❌ المستخدم غير موجود في قاعدة البيانات');
      return {
        found: false,
        message: 'المستخدم غير موجود في قاعدة البيانات'
      };
    }
    
    const userData = userDoc.data();
    console.log('✅ تم جلب بيانات المستخدم من قاعدة البيانات:', {
      username: userData.username,
      expiryDate: userData.expiryDate,
      isActive: userData.isActive
    });
    
    return {
      found: true,
      userData: userData
    };
    
  } catch (error) {
    console.error('❌ خطأ في جلب بيانات المستخدم من قاعدة البيانات:', error);
    throw new Error(`فشل في جلب بيانات المستخدم: ${error.message}`);
  }
}

function startExpiryCheckInterval() {
  // إيقاف أي مؤقت سابق لضمان عدم وجود تكرار
  if (expiryCheckIntervalId) {
    clearInterval(expiryCheckIntervalId);
  }
  
  expiryCheckIntervalId = setInterval(async () => {
    const userSession = localStorage.getItem('userSession');
    if (!userSession) {
      clearInterval(expiryCheckIntervalId);
      return;
    }
    
    try {
      const session = JSON.parse(userSession);
      
      // التحقق من بيانات المستخدم من قاعدة البيانات مباشرة
      const userDataResult = await fetchUserDataFromDatabase(session.userId);
      
      if (!userDataResult.found) {
        console.error('🚨 المستخدم غير موجود في قاعدة البيانات - تسجيل خروج فوري');
        clearInterval(expiryCheckIntervalId);
        showAlert('تم حذف حسابك من النظام', 'error');
        setTimeout(() => {
          logout();
        }, 3000);
        return;
      }
      
      const userData = userDataResult.userData;
      
      // التحقق من حالة الحساب
      if (!userData.isActive) {
        console.error('🚨 تم تعطيل الحساب من قبل الإدارة');
        clearInterval(expiryCheckIntervalId);
        showAlert('تم تعطيل حسابك من قبل الإدارة', 'error');
        setTimeout(() => {
          logout();
        }, 3000);
        return;
      }
      
      // التحقق من تاريخ الصلاحية من قاعدة البيانات
      const expiryCheck = checkExpiryDate(userData.expiryDate);
      
      // تحديث بيانات الجلسة المحلية بالبيانات الجديدة
      const updatedSession = {
        ...session,
        expiryDate: userData.expiryDate,
        displayName: userData.displayName,
        email: userData.email,
        lastDatabaseCheck: new Date().toISOString()
      };
      localStorage.setItem('userSession', JSON.stringify(updatedSession));
      
      // تحديث معلومات الصلاحية في الواجهة
      displayExpiryInfoInHeader(userData.expiryDate);
      
      if (!expiryCheck.isValid) {
        console.error('🚨 تم اكتشاف انتهاء الصلاحية من قاعدة البيانات أثناء الفحص الدوري');
        clearInterval(expiryCheckIntervalId); // إيقاف الفحص
        disableAllFeatures(); // تعطيل الواجهة فوراً
        showAlert(expiryCheck.message, 'error');
        
        // تسجيل الخروج بعد تأخير بسيط لإظهار الرسالة
        setTimeout(() => {
          logout();
        }, 3000);
      } else {
        console.log(`✅ فحص الصلاحية الدوري من قاعدة البيانات: الجلسة صالحة - متبقي ${expiryCheck.daysRemaining} يوم`);
        
        // تحذير إذا كانت الصلاحية ستنتهي خلال 3 أيام
        if (expiryCheck.daysRemaining <= 3 && expiryCheck.daysRemaining > 0) {
          showAlert(`تحذير: ستنتهي صلاحية حسابك خلال ${expiryCheck.daysRemaining} أيام`, 'warning');
        }
      }
    } catch (error) {
      console.error('❌ خطأ أثناء الفحص الدوري للجلسة من قاعدة البيانات:', error);
      
      // في حالة خطأ الشبكة، نستمر بالفحص المحلي كبديل
      if (error.message.includes('network') || error.message.includes('offline')) {
        console.log('⚠️ خطأ في الشبكة - التبديل للفحص المحلي');
        const session = JSON.parse(localStorage.getItem('userSession'));
        const localExpiryCheck = checkExpiryDate(session.expiryDate);
        
        if (!localExpiryCheck.isValid) {
          clearInterval(expiryCheckIntervalId);
          showAlert('انتهت صلاحية الحساب (فحص محلي)', 'error');
          setTimeout(() => {
            logout();
          }, 3000);
        }
      } else {
        // خطأ آخر - إيقاف الفحص
        clearInterval(expiryCheckIntervalId);
        showAlert('حدث خطأ في فحص الصلاحية', 'error');
      }
    }
  }, 120000); // يتم الفحص كل دقيقتين (120 ثانية) لتقليل الحمل على قاعدة البيانات
  
  console.log('🚀 تم بدء الفحص الدوري لصلاحية الجلسة من قاعدة البيانات');
}

// دالة للتحقق من تاريخ انتهاء الصلاحية
function checkExpiryDate(expiryDate) {
  if (!expiryDate) {
    return { 
      isValid: false, 
      message: 'لا يوجد تاريخ انتهاء صلاحية محدد',
      daysRemaining: 0,
      expiryDateFormatted: 'غير محدد'
    };
  }
  
  try {
    const now = new Date();
    let expiry;
    
    // التعامل مع تنسيقات مختلفة للتاريخ
    if (expiryDate.toDate) {
      // Firestore Timestamp
      expiry = expiryDate.toDate();
    } else if (expiryDate.seconds) {
      // كائن بخاصية seconds (من Firebase)
      expiry = new Date(expiryDate.seconds * 1000);
    } else if (typeof expiryDate === 'string' || typeof expiryDate === 'number') {
      // تاريخ عادي
      expiry = new Date(expiryDate);
    } else {
      // محاولة أخيرة
      expiry = new Date(expiryDate);
    }
    
    // التحقق من صحة التاريخ
    if (isNaN(expiry.getTime())) {
      console.error('❌ تاريخ انتهاء الصلاحية غير صحيح:', expiryDate);
      return {
        isValid: false,
        message: 'تاريخ انتهاء الصلاحية غير صحيح',
        daysRemaining: 0,
        expiryDateFormatted: 'غير صحيح'
      };
    }
    
    // حساب الفرق بالأيام
    const timeDiff = expiry.getTime() - now.getTime();
    const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));
    
    const arabicExpiry = expiry.toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    
    if (expiry <= now) {
      return { 
        isValid: false, 
        message: `انتهت صلاحية الحساب في ${arabicExpiry}`,
        daysRemaining: 0,
        expiryDateFormatted: arabicExpiry
      };
    }
    
    return { 
      isValid: true,
      daysRemaining: daysRemaining,
      expiryDateFormatted: arabicExpiry,
      message: `الحساب صالح حتى ${arabicExpiry}`
    };
    
  } catch (error) {
    console.error('❌ خطأ في معالجة تاريخ انتهاء الصلاحية:', error);
    return {
      isValid: false,
      message: 'خطأ في معالجة تاريخ انتهاء الصلاحية',
      daysRemaining: 0,
      expiryDateFormatted: 'خطأ'
    };
  }
}

// فحص الجلسة المحفوظة والتحقق من صحتها
function checkUserSession() {
  const userSession = localStorage.getItem('userSession');
  
  if (!userSession) {
    console.log('👤 لا توجد جلسة محفوظة - إعادة توجيه إلى صفحة تسجيل الدخول');
    showAlert('يجب تسجيل الدخول أولاً', 'warning');
    
    setTimeout(() => {
      window.location.href = 'login.html';
    }, 2000);
    return false;
  }
  
  try {
    const session = JSON.parse(userSession);
    
    // التحقق من تاريخ انتهاء الصلاحية
    const expiryCheck = checkExpiryDate(session.expiryDate);
    if (!expiryCheck.isValid) {
      localStorage.removeItem('userSession');
      showAlert(expiryCheck.message, 'error');
      
      setTimeout(() => {
        window.location.href = 'login.html';
      }, 3000);
      return false;
    }
    
    console.log('👤 المستخدم مسجل الدخول في لوحة التحكم:', session.username);
    updateUserInfo(session);

    // تفعيل وظائف WhatsApp Manager بعد التحقق من الجلسة
    if (window.whatsappManager && typeof window.whatsappManager.enableFunctionsAfterLogin === 'function') {
      window.whatsappManager.enableFunctionsAfterLogin();
    }

    return true;
    
  } catch (error) {
    console.error('خطأ في قراءة بيانات الجلسة:', error);
    localStorage.removeItem('userSession');
    showAlert('حدث خطأ في قراءة بيانات الجلسة', 'error');
    
    setTimeout(() => {
      window.location.href = 'login.html';
    }, 2000);
    return false;
  }
}

// تشغيل الوظائف عند تحميل الصفحة
window.addEventListener('load', () => {
  console.log('📱 تم تحميل لوحة التحكم');
  
  // فحص الجلسة أولاً
  if (checkUserSession()) {
    // تحديث الوقت كل دقيقة
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000);
    
    // إضافة التأثيرات البصرية
    addVisualEffects();

    // بدء الفحص الدوري للصلاحية
    startExpiryCheckInterval();
    

    
    // إضافة رسالة ترحيب
    setTimeout(() => {
      showAlert('مرحباً بك في WhatsApp Blue! 🎉', 'success');
    }, 1000);
  }
});

// معالج أحداث اختصارات لوحة المفاتيح
document.addEventListener('keydown', (e) => {
  // Ctrl + L لتسجيل الخروج
  if (e.ctrlKey && e.key === 'l') {
    e.preventDefault();
    logout();
  }
  
  // Escape لإغلاق التنبيهات
  if (e.key === 'Escape') {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      if (alert.parentNode) {
        alert.parentNode.removeChild(alert);
      }
    });
  }
});

// إضافة أنماط CSS للتنبيهات والرسوم المتحركة
const styles = document.createElement('style');
styles.textContent = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .feature-card:hover {
    cursor: pointer;
  }
  
  .stat-card {
    cursor: default;
  }
  
  .user-expiry {
    display: block;
    font-size: 12px;
    font-weight: 600;
    margin-top: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }
  
  .user-expiry:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }
`;
document.head.appendChild(styles);



// وظيفة تحديث الصفحة
function refreshDashboard() {
    showAlert('جاري تحديث البيانات...', 'info');
    
    // إعادة تحميل الحسابات من whatsappManager إذا كان متوفراً
    if (window.whatsappManager && typeof window.whatsappManager.loadAccounts === 'function') {
        window.whatsappManager.loadAccounts();
    } else if (typeof loadAccounts === 'function') {
        loadAccounts();
    }
    
    setTimeout(() => {
        showAlert('تم تحديث البيانات بنجاح', 'success');
    }, 1000);
}

// وظائف التصفية
let currentFilter = 'all';

function filterAccounts(status) {
    currentFilter = status;
    
    // تحديث أزرار التصفية
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeBtn = document.querySelector(`[onclick="filterAccounts('${status}')"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
    
    // تصفية البطاقات
    const accountCards = document.querySelectorAll('.account-card');
    accountCards.forEach(card => {
        const cardStatus = card.dataset.status;
        
        if (status === 'all' || cardStatus === status) {
            card.style.display = 'block';
            card.style.animation = 'fadeInUp 0.5s ease-out';
        } else {
            card.style.display = 'none';
        }
    });
    
    // تحديث عداد النتائج
    updateFilterResults();
}

function updateFilterResults() {
    const visibleCards = document.querySelectorAll('.account-card[style*="display: block"], .account-card:not([style*="display: none"])');
    const resultCount = visibleCards.length;
    
    // يمكن إضافة عرض عدد النتائج هنا إذا لزم الأمر
}







// تصدير الوظائف للاستخدام الخارجي
window.dashboardFunctions = {
  logout,
  showAlert,
  updateUserInfo,
  refreshDashboard,
  filterAccounts
};