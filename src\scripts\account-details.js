// إدارة صفحة تفاصيل الحساب
class AccountDetailsManager {
    constructor() {
        this.sessionId = null;
        this.account = null;
        this.contacts = [];
        this.filteredContacts = [];
        this.notificationTimeout = null;

        // متغيرات pagination
        this.currentPage = 1;
        this.itemsPerPage = 50;
        this.totalPages = 1;

        this.init();
    }

    init() {
        // الحصول على معرف الجلسة من URL
        const urlParams = new URLSearchParams(window.location.search);
        this.sessionId = urlParams.get('sessionId');
        
        if (!this.sessionId) {
            this.showNotification('معرف الحساب غير موجود', 'error');
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 2000);
            return;
        }

        this.setupEventListeners();
        this.loadAccountDetails();
        this.loadContacts();
    }

    setupEventListeners() {
        // البحث في جهات الاتصال
        const searchInput = document.getElementById('contactSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterContacts(e.target.value);
            });
        }

        // تغيير عدد العناصر في الصفحة
        const itemsPerPageSelect = document.getElementById('itemsPerPage');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', (e) => {
                this.itemsPerPage = parseInt(e.target.value);
                this.currentPage = 1;
                this.displayContacts();
            });
        }

        // إغلاق الإشعارات
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('notification-close') ||
                e.target.closest('.notification-close')) {
                this.hideNotification();
            }
        });
    }

    // تحميل تفاصيل الحساب
    async loadAccountDetails() {
        try {
            // استدعاء API للحصول على تفاصيل الحساب
            const response = await (window.electronAPI ? 
                window.electronAPI.getSessionStatus(this.sessionId) : 
                { success: false, error: 'Electron API not available' });
            
            if (response.success && response.session) {
                this.account = response.session;
                this.displayAccountInfo();
            } else {
                this.showNotification('فشل في تحميل تفاصيل الحساب', 'error');
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);
            }
        } catch (error) {
            console.error('خطأ في تحميل تفاصيل الحساب:', error);
            this.showNotification('حدث خطأ أثناء تحميل تفاصيل الحساب', 'error');
        }
    }

    // عرض تفاصيل الحساب
    displayAccountInfo() {
        if (!this.account) return;

        console.log('عرض تفاصيل الحساب:', this.account);

        // عرض معلومات الحساب في الرأس
        const accountName = document.getElementById('accountName');
        const accountPhone = document.getElementById('accountPhone');
        const whatsappName = document.getElementById('whatsappName');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const messageCount = document.getElementById('messageCount');
        const contactCount = document.getElementById('contactCount');
        const chatCount = document.getElementById('chatCount');

        // تحديث الاسم
        if (accountName) {
            const displayName = this.account.userInfo?.name ||
                              this.account.accountName ||
                              this.account.name ||
                              this.account.id ||
                              'غير محدد';
            accountName.textContent = displayName;
        }

        // تحديث رقم الهاتف
        if (accountPhone) {
            const phoneNumber = this.account.phoneNumber ||
                              this.account.userInfo?.phone ||
                              this.account.phone ||
                              'غير محدد';
            accountPhone.textContent = this.formatPhoneNumber(phoneNumber);
        }

        // تحديث اسم الواتساب
        if (whatsappName) {
            const whatsappDisplayName = this.account.userInfo?.pushname ||
                                      this.account.userInfo?.name ||
                                      this.account.whatsappName ||
                                      'غير محدد';
            whatsappName.textContent = whatsappDisplayName;
        }

        // تحديث حالة الاتصال
        if (statusIndicator && statusText) {
            const status = this.account.status || 'unknown';
            const statusClass = this.getStatusClass(status);
            const statusMessage = this.getStatusText(status);

            statusIndicator.className = `status-indicator ${statusClass}`;
            statusText.textContent = statusMessage;
        }

        // تحديث الإحصائيات
        if (messageCount) {
            const messages = this.account.messageCount ||
                           this.account.stats?.messages ||
                           0;
            messageCount.textContent = this.formatNumber(messages);
        }

        if (contactCount) {
            const contacts = this.account.contactCount ||
                           this.account.stats?.contacts ||
                           0;
            contactCount.textContent = this.formatNumber(contacts);
        }

        if (chatCount) {
            const chats = this.account.chatCount ||
                        this.account.stats?.chats ||
                        0;
            chatCount.textContent = this.formatNumber(chats);
        }
    }

    // تحميل جهات الاتصال مع التحديث التدريجي
    async loadContacts(useCache = true, forceRefresh = false) {
        try {
            // إذا كان هناك جهات اتصال محملة بالفعل ولا يوجد طلب لإعادة التحديث، لا تحمل مرة أخرى
            if (this.contacts.length > 0 && !forceRefresh) {
                console.log('Contacts already loaded, skipping reload');
                this.displayContacts();
                return;
            }
            
            this.showContactsLoading(true);
            
            // إعداد مستمع التحديثات التدريجية
            if (window.electronAPI && window.electronAPI.onContactsProgress) {
                window.electronAPI.onContactsProgress((progressData) => {
                    this.handleContactsProgress(progressData);
                });
            }
            
            // استدعاء API للحصول على جهات الاتصال
            const response = await (window.electronAPI ? 
                window.electronAPI.getContacts(this.sessionId, useCache) : 
                this.mockElectronAPI.getContacts(this.sessionId));
            
            if (response.success) {
                this.contacts = response.contacts || [];
                this.filteredContacts = [...this.contacts];
                this.displayContacts();
                
                const cacheStatus = response.fromCache ? ' (من التخزين المؤقت)' : '';
                console.log(`Loaded ${this.contacts.length} contacts${cacheStatus}`);
                
                if (response.fromCache) {
                    this.showNotification(`تم تحميل ${this.contacts.length} جهة اتصال من التخزين المؤقت`, 'success', 3000);
                }
            } else {
                this.showNotification('فشل في تحميل جهات الاتصال', 'error');
            }
        } catch (error) {
            console.error('خطأ في تحميل جهات الاتصال:', error);
            this.showNotification('حدث خطأ أثناء تحميل جهات الاتصال', 'error');
        } finally {
            if (!useCache) {
                this.showContactsLoading(false);
            }
        }
    }

    // معالجة تحديثات جهات الاتصال التدريجية
    handleContactsProgress(progressData) {
        // التأكد من أن التحديث للجلسة الصحيحة
        if (progressData.sessionId !== this.sessionId) {
            return;
        }
        
        console.log('Contacts progress:', progressData.type, progressData);
        
        switch (progressData.type) {
            case 'cached_data':
                // عرض البيانات المحفوظة فوراً
                this.contacts = progressData.contacts || [];
                this.filteredContacts = [...this.contacts];
                this.displayContacts();
                this.showNotification(`تم تحميل ${this.contacts.length} جهة اتصال من التخزين المؤقت`, 'info');
                break;
                
            case 'loading_start':
                this.showContactsLoading(true);
                this.showNotification(progressData.message || 'جاري تحميل جهات الاتصال...', 'info');
                break;
                
            case 'contacts_filtered':
                this.showNotification(progressData.message || `تم العثور على ${progressData.total} جهة اتصال`, 'info');
                break;
                
            case 'contacts_processed':
                // تحديث جهات الاتصال أثناء المعالجة
                if (progressData.contacts) {
                    this.contacts = progressData.contacts;
                    this.filteredContacts = [...this.contacts];
                    this.displayContacts();
                }
                
                // تحديث شريط التقدم
                this.updateProgressBar(progressData.processed, progressData.total);
                
                if (progressData.message) {
                    this.updateLoadingMessage(progressData.message);
                }
                break;
                
            case 'basic_data_complete':
                // البيانات الأساسية مكتملة
                this.contacts = progressData.contacts || [];
                this.filteredContacts = [...this.contacts];
                this.displayContacts();
                this.showContactsLoading(false);
                this.hideNotification(); // إخفاء الرسالة فوراً
                this.showNotification(progressData.message || `تم تحميل ${this.contacts.length} جهة اتصال بنجاح`, 'success', 3000);
                break;
                
            case 'pictures_loaded':
                // تحديث الصور تدريجياً
                if (progressData.contacts) {
                    this.contacts = progressData.contacts;
                    this.filteredContacts = [...this.contacts];
                    this.displayContacts();
                }
                
                // إذا تم تحميل جميع الصور، إخفاء رسالة التحميل نهائياً
                if (progressData.picturesLoaded >= progressData.totalContacts) {
                    this.hideNotification();
                    console.log('All profile pictures loaded successfully');
                }
                break;
                
            case 'data_updated':
                // تحديث البيانات في الخلفية
                if (progressData.contacts) {
                    this.contacts = progressData.contacts;
                    this.filteredContacts = [...this.contacts];
                    this.displayContacts();
                    this.showNotification(progressData.message || 'تم تحديث بيانات جهات الاتصال', 'success');
                }
                break;
        }
    }

    // تحديث شريط التقدم
    updateProgressBar(processed, total) {
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar && total > 0) {
            const percentage = Math.round((processed / total) * 100);
            progressBar.style.width = `${percentage}%`;
        }
    }

    // تحديث رسالة التحميل
    updateLoadingMessage(message) {
        const loadingMessage = document.querySelector('.loading-message');
        if (loadingMessage) {
            loadingMessage.textContent = message;
        }
    }

    // عرض جهات الاتصال في الجدول مع pagination
    displayContacts() {
        const loadingState = document.getElementById('loadingState');
        const emptyState = document.getElementById('emptyState');
        const contactsTable = document.getElementById('contactsTable');
        const tableBody = document.getElementById('contactsTableBody');
        const paginationContainer = document.getElementById('paginationContainer');

        // إخفاء حالة التحميل
        if (loadingState) loadingState.style.display = 'none';

        if (this.filteredContacts.length === 0) {
            // إظهار حالة الفراغ
            if (emptyState) emptyState.style.display = 'flex';
            if (contactsTable) contactsTable.style.display = 'none';
            if (paginationContainer) paginationContainer.style.display = 'none';
            return;
        }

        // إظهار الجدول
        if (emptyState) emptyState.style.display = 'none';
        if (contactsTable) contactsTable.style.display = 'table';

        if (!tableBody) return;

        // ترتيب جهات الاتصال أبجدياً باللغة العربية
        const sortedContacts = [...this.filteredContacts].sort((a, b) => {
            const nameA = (a.name || a.pushname || '').trim();
            const nameB = (b.name || b.pushname || '').trim();

            // ترتيب أبجدي باللغة العربية
            return nameA.localeCompare(nameB, 'ar', {
                numeric: true,
                sensitivity: 'base',
                ignorePunctuation: true
            });
        });

        // حساب pagination
        this.totalPages = Math.ceil(sortedContacts.length / this.itemsPerPage);
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const currentPageContacts = sortedContacts.slice(startIndex, endIndex);

        // مسح المحتوى السابق
        tableBody.innerHTML = '';

        // إضافة جهات الاتصال للصفحة الحالية
        currentPageContacts.forEach(contact => {
            const row = this.createContactRow(contact);
            tableBody.appendChild(row);
        });

        // تحديث عناصر pagination
        this.updatePaginationControls(sortedContacts.length);
    }

    // تحديث عناصر التحكم في pagination
    updatePaginationControls(totalContacts) {
        const paginationContainer = document.getElementById('paginationContainer');
        const paginationInfo = document.getElementById('paginationInfo');
        const firstPageBtn = document.getElementById('firstPageBtn');
        const prevPageBtn = document.getElementById('prevPageBtn');
        const nextPageBtn = document.getElementById('nextPageBtn');
        const lastPageBtn = document.getElementById('lastPageBtn');
        const pageNumbers = document.getElementById('pageNumbers');

        if (!paginationContainer) return;

        // إظهار/إخفاء pagination حسب عدد الصفحات
        if (this.totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        } else {
            paginationContainer.style.display = 'flex';
        }

        // تحديث معلومات الصفحة
        if (paginationInfo) {
            const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
            const endItem = Math.min(this.currentPage * this.itemsPerPage, totalContacts);
            paginationInfo.textContent = `عرض ${startItem}-${endItem} من ${totalContacts} جهة اتصال`;
        }

        // تحديث حالة الأزرار
        if (firstPageBtn) firstPageBtn.disabled = this.currentPage === 1;
        if (prevPageBtn) prevPageBtn.disabled = this.currentPage === 1;
        if (nextPageBtn) nextPageBtn.disabled = this.currentPage === this.totalPages;
        if (lastPageBtn) lastPageBtn.disabled = this.currentPage === this.totalPages;

        // تحديث أرقام الصفحات
        if (pageNumbers) {
            pageNumbers.innerHTML = this.generatePageNumbers();
        }
    }

    // إنشاء أرقام الصفحات
    generatePageNumbers() {
        let html = '';
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

        // تعديل startPage إذا كان endPage في النهاية
        if (endPage - startPage < maxVisiblePages - 1) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // إضافة نقاط في البداية إذا لزم الأمر
        if (startPage > 1) {
            html += `<span class="page-number" onclick="goToPage(1)">1</span>`;
            if (startPage > 2) {
                html += `<span class="page-ellipsis">...</span>`;
            }
        }

        // إضافة أرقام الصفحات المرئية
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'active' : '';
            html += `<span class="page-number ${activeClass}" onclick="goToPage(${i})">${i}</span>`;
        }

        // إضافة نقاط في النهاية إذا لزم الأمر
        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                html += `<span class="page-ellipsis">...</span>`;
            }
            html += `<span class="page-number" onclick="goToPage(${this.totalPages})">${this.totalPages}</span>`;
        }

        return html;
    }

    // إنشاء صف جهة اتصال
    createContactRow(contact) {
        const row = document.createElement('tr');
        
        // عمود الصورة
        const avatarCell = document.createElement('td');
        if (contact.profilePicUrl) {
            avatarCell.innerHTML = `<img src="${contact.profilePicUrl}" alt="${contact.name || contact.pushname}" class="contact-avatar">`;
        } else {
            avatarCell.innerHTML = `
                <div class="contact-avatar-placeholder">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2M12 11a4 4 0 100-8 4 4 0 000 8z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            `;
        }
        row.appendChild(avatarCell);

        // عمود الاسم مع مؤشر الحفظ
        const nameCell = document.createElement('td');
        nameCell.className = 'contact-name-cell';
        
        // تحديد الاسم المحفوظ والمعروض
        const savedName = contact.name || contact.shortName || 'غير محفوظ';
        const whatsappName = contact.pushname || contact.verifiedName;
        const isMyContact = contact.isMyContact || false;
        
        // إنشاء محتوى الاسم مع مؤشر الحفظ
        let nameContent = `
            <div class="contact-name-container">
                <div class="contact-saved-name">
                    ${isMyContact ? '<span class="saved-indicator" title="محفوظ في جهات الاتصال">📱</span>' : ''}
                    ${this.escapeHtml(savedName)}
                </div>
                ${whatsappName && whatsappName !== savedName ? `<div class="contact-whatsapp-name">${this.escapeHtml(whatsappName)}</div>` : ''}
                ${contact.verifiedName ? `<div class="contact-verified-name" title="حساب تجاري مؤكد">✓ ${this.escapeHtml(contact.verifiedName)}</div>` : ''}
            </div>
        `;
        
        nameCell.innerHTML = nameContent;
        row.appendChild(nameCell);

        // عمود رقم الهاتف
        const phoneCell = document.createElement('td');
        phoneCell.className = 'contact-phone';
        phoneCell.textContent = this.formatPhoneNumber(contact.number);
        row.appendChild(phoneCell);

        // عمود حالة WhatsApp
        const statusCell = document.createElement('td');
        statusCell.className = 'whatsapp-status';
        const hasWhatsApp = contact.isWAContact !== false; // افتراض أن لديه WhatsApp إذا لم يتم تحديد العكس
        
        statusCell.innerHTML = `
            <span class="status-badge ${hasWhatsApp ? 'has-whatsapp' : 'no-whatsapp'}">
                <span class="status-icon"></span>
                ${hasWhatsApp ? 'نعم' : 'لا'}
            </span>
        `;
        row.appendChild(statusCell);

        return row;
    }

    // تصفية جهات الاتصال
    filterContacts(searchTerm) {
        if (!searchTerm.trim()) {
            this.filteredContacts = [...this.contacts];
        } else {
            const term = searchTerm.toLowerCase().trim();
            this.filteredContacts = this.contacts.filter(contact => {
                const name = (contact.name || '').toLowerCase();
                const pushname = (contact.pushname || '').toLowerCase();
                const shortName = (contact.shortName || '').toLowerCase();
                const verifiedName = (contact.verifiedName || '').toLowerCase();
                const number = (contact.number || '').toLowerCase();
                
                return name.includes(term) || 
                       pushname.includes(term) || 
                       shortName.includes(term) ||
                       verifiedName.includes(term) ||
                       number.includes(term);
            });
        }

        // إعادة تعيين الصفحة الحالية إلى 1 عند البحث
        this.currentPage = 1;
        this.displayContacts();
    }

    // تنسيق رقم الهاتف
    formatPhoneNumber(phoneNumber) {
        if (!phoneNumber) return '';
        
        // إزالة المسافات والرموز
        const cleanNumber = phoneNumber.replace(/[\s\-\(\)]/g, '');
        
        // إضافة + إذا لم تكن موجودة
        const formattedNumber = cleanNumber.startsWith('+') ? cleanNumber : `+${cleanNumber}`;
        
        return formattedNumber;
    }

    // تنسيق الأرقام
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'م';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'ك';
        }
        return num.toString();
    }

    // الحصول على فئة CSS للحالة
    getStatusClass(status) {
        switch (status) {
            case 'connected': return 'status-connected';
            case 'connecting': return 'status-connecting';
            case 'disconnected': return 'status-disconnected';
            default: return 'status-unknown';
        }
    }

    // الحصول على نص الحالة
    getStatusText(status) {
        switch (status) {
            case 'connected': return 'متصل';
            case 'connecting': return 'جاري الاتصال';
            case 'disconnected': return 'غير متصل';
            default: return 'غير معروف';
        }
    }

    // تنظيف النص من HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // عرض الإشعارات
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.getElementById('notification');
        if (!notification) return;

        // إلغاء أي timeout سابق
        if (this.notificationTimeout) {
            clearTimeout(this.notificationTimeout);
        }

        const messageElement = notification.querySelector('.notification-message');
        if (messageElement) {
            messageElement.textContent = message;
        }

        notification.className = `notification ${type} show`;

        // إخفاء الإشعار تلقائياً بعد المدة المحددة
        if (duration > 0) {
            this.notificationTimeout = setTimeout(() => {
                this.hideNotification();
            }, duration);
        }
    }

    // إخفاء الإشعارات
    hideNotification() {
        const notification = document.getElementById('notification');
        if (notification) {
            notification.classList.remove('show');
        }

        // إلغاء timeout إذا كان موجوداً
        if (this.notificationTimeout) {
            clearTimeout(this.notificationTimeout);
            this.notificationTimeout = null;
        }
    }

    showLoading(show) {
        const loadingElement = document.getElementById('accountLoading');
        if (loadingElement) {
            loadingElement.style.display = show ? 'flex' : 'none';
        }
    }

    showContactsLoading(show) {
        const loadingState = document.getElementById('loadingState');
        const contactsTable = document.getElementById('contactsTable');
        const emptyState = document.getElementById('emptyState');
        
        if (show) {
            if (loadingState) loadingState.style.display = 'flex';
            if (contactsTable) contactsTable.style.display = 'none';
            if (emptyState) emptyState.style.display = 'none';
        } else {
            if (loadingState) loadingState.style.display = 'none';
        }
    }
}

// الوظائف العامة
function goBack() {
    window.location.href = 'dashboard.html';
}

function loadContacts(useCache = false) {
    if (window.accountDetailsManager) {
        window.accountDetailsManager.loadContacts(useCache, false);
    }
}

// دالة تحديث جهات الاتصال يدوياً
function refreshContacts() {
    if (window.accountDetailsManager) {
        console.log('Manually refreshing contacts...');
        window.accountDetailsManager.loadContacts(false, true); // عدم استخدام التخزين المؤقت وإجبار التحديث
    }
}

// وظائف التنقل بين الصفحات (عامة للاستخدام من HTML)
function goToPage(pageNumber) {
    if (window.accountDetailsManager) {
        window.accountDetailsManager.currentPage = pageNumber;
        window.accountDetailsManager.displayContacts();
    }
}

function goToNextPage() {
    if (window.accountDetailsManager && window.accountDetailsManager.currentPage < window.accountDetailsManager.totalPages) {
        window.accountDetailsManager.currentPage++;
        window.accountDetailsManager.displayContacts();
    }
}

function goToPreviousPage() {
    if (window.accountDetailsManager && window.accountDetailsManager.currentPage > 1) {
        window.accountDetailsManager.currentPage--;
        window.accountDetailsManager.displayContacts();
    }
}

function goToLastPage() {
    if (window.accountDetailsManager) {
        window.accountDetailsManager.currentPage = window.accountDetailsManager.totalPages;
        window.accountDetailsManager.displayContacts();
    }
}

// تهيئة المدير عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.accountDetailsManager = new AccountDetailsManager();
});

// تنظيف الموارد عند إغلاق النافذة
window.addEventListener('beforeunload', () => {
    if (window.accountDetailsManager) {
        // تنظيف مستمعي أحداث جهات الاتصال
        if (window.electronAPI && window.electronAPI.removeContactsProgressListener) {
            window.electronAPI.removeContactsProgressListener();
        }
    }
});

// تنظيف المستمعين عند تغيير الصفحة
window.addEventListener('pagehide', () => {
    if (window.electronAPI && window.electronAPI.removeContactsProgressListener) {
        window.electronAPI.removeContactsProgressListener();
    }
});

// إضافة دعم للـ Electron API إذا لم يكن موجوداً
if (!window.electronAPI) {
    window.electronAPI = {
        getAllSessions: async () => {
            console.warn('Electron API not available - using mock data');
            return { success: false, error: 'Electron API not available' };
        },
        getContacts: async (sessionId) => {
            console.warn('Electron API not available - using mock data');
            return { success: false, error: 'Electron API not available' };
        }
    };
}