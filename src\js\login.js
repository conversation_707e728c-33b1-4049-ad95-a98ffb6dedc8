// استخدام Firebase من المتغيرات العامة
// سيتم تحميل Firebase من firebase-config.js

// متغيرات عامة
const loginForm = document.getElementById('loginForm');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const loginBtn = document.getElementById('loginBtn');
const rememberMeCheckbox = document.getElementById('rememberMe');
const forgotPasswordLink = document.querySelector('.forgot-password');
const signupLink = document.getElementById('signupLink');

// دالة لإظهار/إخفاء مؤشر التحميل
function toggleLoading(button, isLoading) {
  const btnText = button.querySelector('.btn-text');
  const spinner = button.querySelector('.loading-spinner');
  
  if (isLoading) {
    btnText.style.display = 'none';
    spinner.style.display = 'block';
    button.disabled = true;
  } else {
    btnText.style.display = 'block';
    spinner.style.display = 'none';
    button.disabled = false;
  }
}

// دالة لإظهار رسائل الخطأ
function showError(inputElement, message) {
  const formGroup = inputElement.closest('.form-group');
  const errorElement = formGroup.querySelector('.error-message');
  
  formGroup.classList.add('error');
  formGroup.classList.remove('success');
  errorElement.textContent = message;
  errorElement.classList.add('show');
}

// دالة لإخفاء رسائل الخطأ
function hideError(inputElement) {
  const formGroup = inputElement.closest('.form-group');
  const errorElement = formGroup.querySelector('.error-message');
  
  formGroup.classList.remove('error');
  formGroup.classList.add('success');
  errorElement.classList.remove('show');
}

// دالة لإظهار رسائل التنبيه
function showAlert(message, type = 'info') {
  const alert = document.createElement('div');
  alert.className = `alert alert-${type}`;
  alert.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  `;
  
  const colors = {
    success: '#28a745',
    error: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8'
  };
  
  alert.style.backgroundColor = colors[type] || colors.info;
  alert.textContent = message;
  
  document.body.appendChild(alert);
  
  setTimeout(() => {
    alert.style.animation = 'slideOut 0.3s ease-in';
    setTimeout(() => {
      if (alert.parentNode) {
        alert.parentNode.removeChild(alert);
      }
    }, 300);
  }, 5000);
}

// ملاحظة: تم استبدال الدوال القديمة بدوال محسنة جديدة
// validateUsername, validatePassword, checkExpiryDate
// تم استبدالها بـ validateLoginInput, verifyPassword, validateExpiryDate

// دالة محسنة للتحقق من صحة البيانات المدخلة
function validateLoginInput(username, password) {
  const errors = [];
  
  // التحقق من اسم المستخدم
  if (!username || username.trim().length === 0) {
    errors.push({ field: 'username', message: 'يرجى إدخال اسم المستخدم' });
  } else if (username.trim().length < 3) {
    errors.push({ field: 'username', message: 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' });
  } else if (username.trim().length > 50) {
    errors.push({ field: 'username', message: 'اسم المستخدم طويل جداً (الحد الأقصى 50 حرف)' });
  } else if (!/^[a-zA-Z0-9_.-]+$/.test(username.trim())) {
    errors.push({ field: 'username', message: 'اسم المستخدم يحتوي على أحرف غير مسموحة' });
  }
  
  // التحقق من كلمة المرور
  if (!password || password.length === 0) {
    errors.push({ field: 'password', message: 'يرجى إدخال كلمة المرور' });
  } else if (password.length < 6) {
    errors.push({ field: 'password', message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' });
  } else if (password.length > 128) {
    errors.push({ field: 'password', message: 'كلمة المرور طويلة جداً (الحد الأقصى 128 حرف)' });
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

// دالة محسنة للبحث عن المستخدم في قاعدة البيانات
async function searchUserInDatabase(username) {
  try {
    console.log('🔍 البحث عن المستخدم في قاعدة البيانات:', username);
    
    const db = window.firebaseDb || firebase.firestore();
    
    // البحث في مجموعة المستخدمين باستخدام اسم المستخدم
    const querySnapshot = await db.collection('users')
      .where('username', '==', username.trim().toLowerCase())
      .limit(1)
      .get();
    
    if (querySnapshot.empty) {
      console.log('❌ لم يتم العثور على المستخدم:', username);
      return {
        found: false,
        message: 'اسم المستخدم غير موجود في قاعدة البيانات'
      };
    }
    
    const userDoc = querySnapshot.docs[0];
    const userData = userDoc.data();
    
    console.log('✅ تم العثور على المستخدم:', {
      id: userDoc.id,
      username: userData.username,
      displayName: userData.displayName,
      hasPassword: !!userData.password,
      expiryDate: userData.expiryDate,
      createdAt: userData.createdAt
    });
    
    return {
      found: true,
      docId: userDoc.id,
      userData: userData
    };
    
  } catch (error) {
    console.error('❌ خطأ في البحث عن المستخدم:', error);
    throw new Error(`فشل في البحث عن المستخدم: ${error.message}`);
  }
}

// دالة محسنة للتحقق من كلمة المرور
function verifyPassword(inputPassword, storedPassword) {
  if (!storedPassword) {
    console.log('❌ كلمة المرور غير محفوظة في قاعدة البيانات');
    return {
      isValid: false,
      message: 'كلمة المرور غير محفوظة في النظام'
    };
  }
  
  // مقارنة كلمة المرور (يمكن تحسينها لاحقاً باستخدام التشفير)
  const isMatch = inputPassword === storedPassword;
  
  console.log('🔐 نتيجة التحقق من كلمة المرور:', isMatch ? 'صحيحة' : 'خاطئة');
  
  return {
    isValid: isMatch,
    message: isMatch ? 'كلمة المرور صحيحة' : 'كلمة المرور غير صحيحة'
  };
}

// دالة محسنة للتحقق من تاريخ انتهاء الصلاحية
function validateExpiryDate(expiryDate) {
  if (!expiryDate || typeof expiryDate.seconds !== 'number') {
    console.log('⚠️ لا يوجد تاريخ انتهاء صلاحية محدد أو أن التنسيق غير صحيح');
    return {
      isValid: true,
      message: 'لا يوجد تاريخ انتهاء صلاحية',
      daysRemaining: null
    };
  }
  
  try {
    const expiry = new Date(expiryDate.seconds * 1000); // تحويل الثواني إلى ميلي ثانية
    const now = new Date();
    
    // التحقق من صحة التاريخ
    if (isNaN(expiry.getTime())) {
      console.log('❌ تاريخ انتهاء الصلاحية غير صحيح:', expiryDate);
      return {
        isValid: false,
        message: 'تاريخ انتهاء الصلاحية غير صحيح',
        daysRemaining: null
      };
    }
    
    const timeDiff = expiry.getTime() - now.getTime();
    const daysRemaining = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    
    if (timeDiff < 0) {
      const arabicExpiry = expiry.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      
      console.log('❌ انتهت صلاحية الحساب في:', arabicExpiry);
      
      return {
        isValid: false,
        message: `انتهت صلاحية الحساب في ${arabicExpiry}`,
        daysRemaining: daysRemaining
      };
    }
    
    console.log('✅ الحساب صالح، الأيام المتبقية:', daysRemaining);
    
    // تحذير إذا كانت الصلاحية ستنتهي خلال 7 أيام
    let warningMessage = '';
    if (daysRemaining <= 7 && daysRemaining > 0) {
      warningMessage = ` (تنبيه: ستنتهي الصلاحية خلال ${daysRemaining} أيام)`;
    }
    
    return {
      isValid: true,
      message: `الحساب صالح${warningMessage}`,
      daysRemaining: daysRemaining,
      hasWarning: daysRemaining <= 7
    };
    
  } catch (error) {
    console.error('❌ خطأ في التحقق من تاريخ انتهاء الصلاحية:', error);
    return {
      isValid: false,
      message: 'خطأ في التحقق من تاريخ انتهاء الصلاحية',
      daysRemaining: null
    };
  }
}

// دالة تسجيل الدخول المحسنة
async function loginWithCustomAuth(username, password) {
  try {
    toggleLoading(loginBtn, true);
    
    console.log('🚀 بدء عملية تسجيل الدخول للمستخدم:', username);
    
    // 1. التحقق من صحة البيانات المدخلة
    const inputValidation = validateLoginInput(username, password);
    if (!inputValidation.isValid) {
      console.log('❌ فشل التحقق من صحة البيانات المدخلة');
      
      inputValidation.errors.forEach(error => {
        if (error.field === 'username') {
          showError(usernameInput, error.message);
        } else if (error.field === 'password') {
          showError(passwordInput, error.message);
        }
      });
      
      showAlert('يرجى تصحيح الأخطاء المدخلة', 'error');
      return;
    }
    
    // 2. البحث عن المستخدم في قاعدة البيانات
    const userSearch = await searchUserInDatabase(username);
    if (!userSearch.found) {
      console.log('❌ المستخدم غير موجود');
      showError(usernameInput, userSearch.message);
      showAlert(userSearch.message, 'error');
      return;
    }
    
    const { userData, docId } = userSearch;
    
    // 3. التحقق من كلمة المرور
    const passwordCheck = verifyPassword(password, userData.password);
    if (!passwordCheck.isValid) {
      console.log('❌ كلمة المرور غير صحيحة');
      showError(passwordInput, passwordCheck.message);
      showAlert(passwordCheck.message, 'error');
      return;
    }
    
    // 4. التحقق من تاريخ انتهاء الصلاحية
    const expiryCheck = validateExpiryDate(userData.expiryDate);
    if (!expiryCheck.isValid) {
      console.log('❌ انتهت صلاحية الحساب');
      showAlert(expiryCheck.message, 'error');
      return;
    }
    
    // 5. تسجيل الدخول بنجاح
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    let successMessage = `مرحباً ${userData.displayName || username}! تم تسجيل الدخول بنجاح`;
    if (expiryCheck.hasWarning) {
      successMessage += `\n${expiryCheck.message}`;
    }
    
    showAlert(successMessage, expiryCheck.hasWarning ? 'warning' : 'success');
    
    // 6. حفظ بيانات الجلسة
    const userSession = {
      userId: docId,
      username: userData.username,
      displayName: userData.displayName || username,
      email: userData.email || '',
      loginTime: new Date().toISOString(),
      expiryDate: userData.expiryDate,
      daysRemaining: expiryCheck.daysRemaining
    };
    
    localStorage.setItem('userSession', JSON.stringify(userSession));
    
    // 7. حفظ حالة "تذكرني" إذا كانت مفعلة
    if (rememberMeCheckbox.checked) {
      localStorage.setItem('rememberUser', 'true');
      localStorage.setItem('savedUsername', username.trim().toLowerCase());
    } else {
      localStorage.removeItem('rememberUser');
      localStorage.removeItem('savedUsername');
    }
    
    // 8. تسجيل عملية الدخول (يمكن إضافة هذا لاحقاً)
    console.log('📊 تم تسجيل عملية الدخول:', {
      userId: docId,
      username: userData.username,
      loginTime: userSession.loginTime,
      daysRemaining: expiryCheck.daysRemaining
    });
    
    // 9. الانتقال إلى الصفحة الرئيسية
    setTimeout(() => {
      window.location.href = 'dashboard.html';
    }, 2000);
    
  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error);
    
    let errorMessage = 'حدث خطأ في تسجيل الدخول';
    
    if (error.message.includes('Missing or insufficient permissions')) {
      errorMessage = 'خطأ في صلاحيات قاعدة البيانات. يرجى مراجعة إعدادات Firebase';
    } else if (error.message.includes('network')) {
      errorMessage = 'خطأ في الاتصال بالإنترنت. يرجى التحقق من الاتصال';
    } else if (error.message.includes('quota')) {
      errorMessage = 'تم تجاوز حد الاستخدام المسموح. يرجى المحاولة لاحقاً';
    }
    
    showAlert(errorMessage, 'error');
  } finally {
    toggleLoading(loginBtn, false);
  }
}

// معالج أحداث النموذج المحسن
loginForm.addEventListener('submit', async (e) => {
  e.preventDefault();
  
  // الحصول على البيانات المدخلة
  const username = usernameInput.value.trim();
  const password = passwordInput.value;
  
  console.log('📝 تم إرسال نموذج تسجيل الدخول');
  
  // إخفاء رسائل الخطأ السابقة
  hideError(usernameInput);
  hideError(passwordInput);
  
  // التحقق الأولي من وجود البيانات
  if (!username && !password) {
    showError(usernameInput, 'يرجى إدخال اسم المستخدم');
    showError(passwordInput, 'يرجى إدخال كلمة المرور');
    showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
    return;
  }
  
  // استدعاء دالة تسجيل الدخول المحسنة
  // (التحقق من صحة البيانات سيتم داخل الدالة)
  await loginWithCustomAuth(username, password);
});

// معالج رابط نسيان كلمة المرور
forgotPasswordLink.addEventListener('click', (e) => {
  e.preventDefault();
  showAlert('ميزة استعادة كلمة المرور ستكون متاحة قريباً', 'info');
});

// معالج رابط إنشاء حساب جديد
signupLink.addEventListener('click', (e) => {
  e.preventDefault();
  showAlert('صفحة إنشاء الحساب ستكون متاحة قريباً', 'info');
});

// إزالة رسائل الخطأ عند الكتابة
usernameInput.addEventListener('input', () => hideError(usernameInput));
passwordInput.addEventListener('input', () => hideError(passwordInput));

// تحميل البيانات المحفوظة إذا كان "تذكرني" مفعلاً
window.addEventListener('load', async () => {
  if (localStorage.getItem('rememberUser') === 'true') {
    const savedUsername = localStorage.getItem('savedUsername');
    if (savedUsername) {
      usernameInput.value = savedUsername;
      rememberMeCheckbox.checked = true;
    }
  }
  
  // التحقق من وجود جلسة نشطة
  const userSession = localStorage.getItem('userSession');
  if (userSession) {
    try {
      const session = JSON.parse(userSession);
      console.log('🔍 فحص الجلسة المحفوظة:', {
        username: session.username,
        loginTime: session.loginTime,
        expiryDate: session.expiryDate
      });
      
      const expiryCheck = validateExpiryDate(session.expiryDate);
      
      if (expiryCheck.isValid) {
        console.log('✅ يوجد جلسة نشطة صالحة، إعادة توجيه إلى لوحة التحكم');
        
        // عرض تحذير إذا كانت الصلاحية ستنتهي قريباً
        if (expiryCheck.hasWarning) {
          showAlert(`مرحباً ${session.displayName}! ${expiryCheck.message}`, 'warning');
        }
        
        setTimeout(() => {
          window.location.href = 'dashboard.html';
        }, expiryCheck.hasWarning ? 3000 : 1000);
        return;
      } else {
        // إزالة الجلسة المنتهية الصلاحية
        console.log('❌ انتهت صلاحية الجلسة المحفوظة');
        localStorage.removeItem('userSession');
        showAlert('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى', 'warning');
      }
    } catch (error) {
      console.error('❌ خطأ في قراءة بيانات الجلسة:', error);
      localStorage.removeItem('userSession');
      showAlert('خطأ في بيانات الجلسة المحفوظة، يرجى تسجيل الدخول مرة أخرى', 'error');
    }
  }
});

// إضافة أنماط CSS للتنبيهات
const alertStyles = document.createElement('style');
alertStyles.textContent = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(alertStyles);