<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الحساب - WhatsApp Blue</title>
    <link rel="stylesheet" href="styles/dashboard.css">
    <link rel="stylesheet" href="styles/account-details.css">
</head>
<body>
    <div class="app-container">
        <!-- شريط التنقل العلوي -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-left">
                    <button class="back-btn" onclick="goBack()">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        العودة
                    </button>
                    <h1>تفاصيل الحساب</h1>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <span class="user-name">WhatsApp Blue</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- محتوى الصفحة -->
        <main class="main-content">
            <!-- رأس الصفحة - معلومات الحساب -->
            <div class="account-header" id="accountHeader">
                <div class="account-avatar-large">
                    <div class="avatar-placeholder" id="accountAvatar">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2M12 11a4 4 0 100-8 4 4 0 000 8z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="account-info-large">
                    <h2 class="account-name" id="accountName">جاري التحميل...</h2>
                    <p class="account-phone" id="accountPhone">-</p>
                    <p class="whatsapp-name" id="whatsappName">-</p>
                    <div class="account-status-large">
                        <div class="status-indicator" id="statusIndicator"></div>
                        <span class="status-text" id="statusText">-</span>
                    </div>
                </div>
                <div class="account-stats-large">
                    <div class="stat-card">
                        <div class="stat-number" id="messageCount">0</div>
                        <div class="stat-label">رسالة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="contactCount">0</div>
                        <div class="stat-label">جهة اتصال</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="chatCount">0</div>
                        <div class="stat-label">محادثة</div>
                    </div>
                </div>
            </div>

            <!-- جدول جهات الاتصال -->
            <div class="contacts-section">
                <div class="section-header">
                    <h3>جهات الاتصال</h3>
                    <div class="contacts-controls">
                        <div class="search-box">
                            <input type="text" id="contactSearch" placeholder="البحث في جهات الاتصال...">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="pagination-controls">
                            <select id="itemsPerPage" class="items-per-page">
                                <option value="25">25 سجل</option>
                                <option value="50" selected>50 سجل</option>
                                <option value="100">100 سجل</option>
                            </select>
                        </div>
                        <div class="refresh-buttons">
                            <button class="refresh-btn" onclick="loadContacts(true)" title="تحديث من التخزين المؤقت">
                                <i class="fas fa-sync-alt"></i>
                                <span>تحديث سريع</span>
                            </button>
                            <button class="refresh-btn force-refresh" onclick="loadContacts(false)" title="تحديث كامل من WhatsApp">
                                <i class="fas fa-download"></i>
                                <span>تحديث كامل</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="contacts-table-container">
                    <div class="loading-state" id="loadingState">
                        <div class="loading-spinner"></div>
                        <div class="loading-content">
                            <span class="loading-message">جاري تحميل جهات الاتصال...</span>
                            <div class="progress-container">
                                <div class="progress-bar"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="empty-state" id="emptyState" style="display: none;">
                        <div class="empty-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 21v-2a4 4 0 00-4-4H6a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM22 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <h3>لا توجد جهات اتصال</h3>
                        <p>لم يتم العثور على جهات اتصال بأرقام هواتف واضحة</p>
                    </div>
                    
                    <table class="contacts-table" id="contactsTable" style="display: none;">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>رقم الهاتف</th>
                                <th>حساب WhatsApp</th>
                            </tr>
                        </thead>
                        <tbody id="contactsTableBody">
                            <!-- سيتم إضافة جهات الاتصال هنا ديناميكياً -->
                        </tbody>
                    </table>

                    <!-- عناصر التنقل بين الصفحات -->
                    <div class="pagination-container" id="paginationContainer" style="display: none;">
                        <div class="pagination-info">
                            <span id="paginationInfo">عرض 1-50 من 0 جهة اتصال</span>
                        </div>
                        <div class="pagination-controls-bottom">
                            <button class="pagination-btn" id="firstPageBtn" onclick="goToPage(1)" disabled>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11 19l-7-7 7-7M21 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                الأولى
                            </button>
                            <button class="pagination-btn" id="prevPageBtn" onclick="goToPreviousPage()" disabled>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15 18l-6-6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                السابقة
                            </button>
                            <span class="page-numbers" id="pageNumbers">
                                <!-- سيتم إضافة أرقام الصفحات هنا -->
                            </span>
                            <button class="pagination-btn" id="nextPageBtn" onclick="goToNextPage()">
                                التالية
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="pagination-btn" id="lastPageBtn" onclick="goToLastPage()">
                                الأخيرة
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M13 5l7 7-7 7M3 5l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة الإشعارات -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <span class="notification-message"></span>
            <button class="notification-close">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
    </div>

    <!-- تضمين نظام حماية المصادقة -->
    <script src="js/auth-guard.js"></script>
    
    <script src="scripts/account-details.js"></script>
</body>
</html>