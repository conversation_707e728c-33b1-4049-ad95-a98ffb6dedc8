const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const QRCode = require('qrcode');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const os = require('os');
const ContactsCache = require('./data/contacts-cache');

class WhatsAppManagerWeb {
    constructor(mainWindow) {
        this.mainWindow = mainWindow;
        this.clients = new Map(); // sessionId -> client instance
        this.sessions = new Map(); // sessionId -> session data

        // حالة تسجيل الدخول - لا تبدأ الوظائف إلا بعد تسجيل الدخول
        this.isUserLoggedIn = false;
        this.pendingAutoConnect = false;

        // تحديد مسار التطبيق بطريقة تعمل مع الملف التنفيذي والتطوير
        this.appPath = this.getAppDataPath();
        this.sessionsPath = path.join(this.appPath, 'sessions');
        this.sessionsFile = path.join(this.sessionsPath, 'sessions.json');

        console.log(`App data path: ${this.appPath}`);
        console.log(`Sessions path: ${this.sessionsPath}`);

        // إنشاء مجلد الجلسات إذا لم يكن موجوداً
        this.ensureDirectoryExists(this.sessionsPath);

        // إنشاء نظام التخزين المؤقت لجهات الاتصال
        this.contactsCache = new ContactsCache();

        // تحميل الجلسات المحفوظة عند بدء التطبيق (بدون اتصال تلقائي)
        this.loadSavedSessions();
    }

    /**
     * تحديد مسار بيانات التطبيق بطريقة تعمل مع الملف التنفيذي والتطوير
     */
    getAppDataPath() {
        try {
            // التحقق من وضع التطوير بطرق متعددة
            const isDevelopment = process.env.NODE_ENV === 'development' || 
                                process.execPath.includes('node') || 
                                process.execPath.includes('electron') ||
                                !app.isPackaged;
            
            if (isDevelopment) {
                // في وضع التطوير، استخدم مجلد المشروع
                const projectRoot = process.cwd().includes('node_modules') ? 
                    path.dirname(path.dirname(process.cwd())) : process.cwd();
                return path.join(projectRoot, 'app-data');
            } else {
                // في الملف التنفيذي، استخدم مجلد بجانب الملف التنفيذي
                const execPath = process.execPath;
                const execDir = path.dirname(execPath);
                return path.join(execDir, 'app-data');
            }
        } catch (error) {
            console.error('Error determining app data path:', error);
            // fallback إلى مجلد المشروع الحالي
            const projectRoot = process.cwd().includes('node_modules') ? 
                path.dirname(path.dirname(process.cwd())) : process.cwd();
            return path.join(projectRoot, 'app-data');
        }
    }

    /**
     * التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً
     */
    ensureDirectoryExists(dirPath) {
        try {
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
                console.log(`Created directory: ${dirPath}`);
            }
        } catch (error) {
            console.error(`Failed to create directory ${dirPath}:`, error);
        }
    }

    /**
     * تحميل الجلسات المحفوظة من المجلدات المنفصلة - نسخة محسنة
     */
    loadSavedSessions() {
        try {
            // التحقق من وجود مجلد الجلسات
            if (!fs.existsSync(this.sessionsPath)) {
                console.log('Sessions directory not found, creating...');
                this.ensureDirectoryExists(this.sessionsPath);
                return;
            }

            // قراءة جميع المجلدات في مجلد الجلسات
            const sessionDirs = fs.readdirSync(this.sessionsPath, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name);

            console.log(`Found ${sessionDirs.length} session directories`);

            let loadedCount = 0;
            for (const sessionId of sessionDirs) {
                try {
                    const sessionPath = path.join(this.sessionsPath, sessionId);
                    const sessionInfoFile = path.join(sessionPath, 'session-info.json');
                    
                    // فحص وجود ملفات المصادقة
                    const hasAuthFiles = this.checkSessionAuthFiles(sessionPath, sessionId);
                    
                    // إذا لم توجد ملفات مصادقة، تخطي هذه الجلسة
                    if (!hasAuthFiles) {
                        console.warn(`No authentication files found for ${sessionId}, skipping`);
                        continue;
                    }
                    
                    // محاولة قراءة ملف معلومات الجلسة
                    let sessionInfo = null;
                    if (fs.existsSync(sessionInfoFile)) {
                        try {
                            const sessionInfoData = fs.readFileSync(sessionInfoFile, 'utf8');
                            sessionInfo = JSON.parse(sessionInfoData);
                            console.log(`Loaded session info for ${sessionId}`);
                        } catch (parseError) {
                            console.warn(`Could not parse session-info.json for ${sessionId}:`, parseError.message);
                        }
                    }
                    
                    // إنشاء معلومات جلسة افتراضية إذا لم توجد
                    if (!sessionInfo) {
                        sessionInfo = {
                            id: sessionId,
                            createdAt: new Date().toISOString(),
                            connectionCount: 1, // افتراض أنها جلسة محفوظة
                            status: 'saved'
                        };
                        console.log(`Created default session info for ${sessionId}`);
                    }
                    
                    // التحقق من وجود ملفات الجلسة الفعلية (WhatsApp Web session files)
                    // البحث في المجلد الفرعي للجلسة أيضاً
                    const sessionJsonPath = path.join(sessionPath, 'session.json');
                    const defaultPath = path.join(sessionPath, 'Default');
                    const subSessionPath = path.join(sessionPath, `session-${sessionId}`);
                    const subDefaultPath = path.join(subSessionPath, 'Default');
                    
                    let sessionStatus = 'saved';
                    const hasSessionFiles = fs.existsSync(sessionJsonPath) || 
                                          fs.existsSync(defaultPath) || 
                                          fs.existsSync(subDefaultPath);
                    
                    if (!hasSessionFiles) {
                        console.warn(`WhatsApp session files not found for ${sessionId}, marking as auth_required`);
                        sessionStatus = 'auth_required';
                    }
                    
                    // إضافة الجلسة إلى الذاكرة
                    this.sessions.set(sessionId, {
                        id: sessionInfo.id || sessionId,
                        connectionType: sessionInfo.connectionType || 'saved',
                        phoneNumber: sessionInfo.phoneNumber,
                        userInfo: sessionInfo.userInfo,
                        createdAt: sessionInfo.createdAt,
                        lastConnected: sessionInfo.lastConnected,
                        connectionCount: sessionInfo.connectionCount || 0,
                        messageCount: sessionInfo.messageCount || 0,
                        contactCount: sessionInfo.contactCount || 0,
                        chatCount: sessionInfo.chatCount || 0,
                        status: sessionStatus,
                        version: sessionInfo.version || '2.0.0',
                        sessionPath: sessionPath,
                        deviceInfo: sessionInfo.deviceInfo,
                        whatsappState: sessionInfo.whatsappState,
                        lastUpdated: sessionInfo.lastUpdated,
                        client: null // سيتم إنشاؤه عند الحاجة
                    });
                    
                    loadedCount++;
                    console.log(`Loaded session: ${sessionId} (status: ${sessionStatus})`);
                    
                } catch (sessionError) {
                    console.error(`Error loading session ${sessionId}:`, sessionError);
                }
            }

            console.log(`Successfully loaded ${loadedCount} sessions into memory`);

            // إرسال الجلسات المحفوظة للواجهة الأمامية
            if (this.mainWindow && this.mainWindow.webContents) {
                this.mainWindow.webContents.send('sessions-loaded', {
                    sessions: this.getAllSessions()
                });
            }

            // تحديث فهرس الجلسات بعد التحميل
            this.updateSessionsIndex();

            // تأجيل الاتصال التلقائي حتى تسجيل الدخول
            console.log('🔒 تم تحميل الجلسات المحفوظة، في انتظار تسجيل الدخول لبدء الاتصال التلقائي');
            this.pendingAutoConnect = true;

        } catch (error) {
            console.error('Error loading saved sessions:', error);
        }
    }

    /**
     * تفعيل الوظائف بعد تسجيل الدخول بنجاح
     */
    enableFunctionsAfterLogin() {
        console.log('🔓 تم تسجيل الدخول بنجاح، بدء تفعيل وظائف WhatsApp');
        this.isUserLoggedIn = true;

        // بدء الاتصال التلقائي إذا كان مؤجلاً
        if (this.pendingAutoConnect) {
            console.log('🚀 بدء الاتصال التلقائي بالجلسات المحفوظة');
            setTimeout(() => {
                this.autoConnectSavedSessions();
            }, 1000);
            this.pendingAutoConnect = false;
        }
    }

    /**
     * تعطيل الوظائف عند تسجيل الخروج
     */
    disableFunctionsAfterLogout() {
        console.log('🔒 تم تسجيل الخروج، تعطيل وظائف WhatsApp');
        this.isUserLoggedIn = false;
        this.pendingAutoConnect = true;

        // يمكن إضافة المزيد من عمليات التنظيف هنا إذا لزم الأمر
    }

    /**
     * الاتصال التلقائي بالجلسات المحفوظة
     * LocalAuth يحفظ الجلسة تلقائياً ويستعيدها عند إنشاء Client بنفس clientId
     */
    async autoConnectSavedSessions() {
        // التحقق من تسجيل الدخول قبل البدء
        if (!this.isUserLoggedIn) {
            console.log('🚫 لم يتم تسجيل الدخول بعد، تأجيل الاتصال التلقائي');
            this.pendingAutoConnect = true;
            return;
        }
        console.log('Starting auto-connection to saved sessions...');

        const savedSessions = Array.from(this.sessions.values()).filter(
            session => session.status === 'saved' && !session.client
        );
        
        const authRequiredSessions = Array.from(this.sessions.values()).filter(
            session => session.status === 'auth_required' && !session.client
        );

        if (savedSessions.length === 0 && authRequiredSessions.length === 0) {
            console.log('No sessions found for auto-connection');
            return;
        }

        console.log(`Found ${savedSessions.length} saved sessions and ${authRequiredSessions.length} sessions requiring authentication for auto-connection`);
        
        // إشعار المستخدم بالجلسات التي تحتاج إعادة مصادقة
        if (authRequiredSessions.length > 0) {
            console.log('Sessions requiring re-authentication:');
            authRequiredSessions.forEach(session => {
                console.log(`- ${session.id}: ${session.userInfo?.name || 'Unknown'} (${session.userInfo?.phone || 'No phone'})`);
                
                // إشعار الواجهة الأمامية
                if (this.mainWindow && this.mainWindow.webContents) {
                    this.mainWindow.webContents.send('session-auth-required', {
                        sessionId: session.id,
                        userInfo: session.userInfo,
                        message: 'هذه الجلسة تحتاج إعادة مصادقة. يمكنك محاولة إعادة الاتصال أو حذف الجلسة.',
                        canReconnect: true
                    });
                }
            });
        }

        // الاتصال بالجلسات المحفوظة واحدة تلو الأخرى لتجنب الحمل الزائد
        for (const sessionData of savedSessions) {
            try {
                console.log(`Auto-connecting to saved session: ${sessionData.id}`);

                // تحديث حالة الجلسة
                sessionData.status = 'reconnecting';
                sessionData.lastUpdated = new Date().toISOString();

                // إشعار الواجهة الأمامية بحالة إعادة الاتصال
                if (this.mainWindow && this.mainWindow.webContents) {
                    this.mainWindow.webContents.send('session-reconnecting', {
                        sessionId: sessionData.id,
                        message: 'جاري الاتصال بالجلسة المحفوظة...'
                    });
                }

                // الاتصال بالجلسة المحفوظة (LocalAuth سيستعيد الجلسة تلقائياً)
                await this.connectToSavedSession(sessionData.id);

                // انتظار قصير بين الجلسات لتجنب الحمل الزائد
                await new Promise(resolve => setTimeout(resolve, 3000));

            } catch (error) {
                console.error(`Failed to auto-connect session ${sessionData.id}:`, error);

                // تحديث حالة الجلسة في حالة الفشل
                sessionData.status = 'connection_failed';
                sessionData.lastError = error.message;
                sessionData.lastUpdated = new Date().toISOString();

                // إشعار الواجهة الأمامية بالفشل
                if (this.mainWindow && this.mainWindow.webContents) {
                    this.mainWindow.webContents.send('session-connection-failed', {
                        sessionId: sessionData.id,
                        error: error.message
                    });
                }
            }
        }

        // بدء مراقبة الجلسات بعد تأخير للسماح للجلسات بالاتصال
        setTimeout(() => {
            this.startSessionMonitoring();
        }, 10000); // تأخير 10 ثوان



        // حفظ التحديثات
        await this.updateSessionsIndex();
        console.log('Auto-connection process completed');
    }

    /**
     * محاولة إعادة الاتصال بجلسة محفوظة
     */
    async attemptSessionReconnection(sessionId) {
        try {
            console.log(`Attempting to reconnect session: ${sessionId}`);

            const session = this.sessions.get(sessionId);
            if (!session) {
                console.log(`Session ${sessionId} not found`);
                return;
            }

            // استخدام دالة الاتصال بالجلسة المحفوظة
            const result = await this.connectToSavedSession(sessionId);

            if (!result.success) {
                // في حالة الفشل، إرسال حدث يتطلب إعادة المصادقة
                if (this.mainWindow && this.mainWindow.webContents) {
                    this.mainWindow.webContents.send('session-auth-required', {
                        sessionId,
                        userInfo: session.userInfo,
                        message: `فشل في إعادة الاتصال: ${result.error}`,
                        canReconnect: true
                    });
                }
            }

        } catch (error) {
            console.error(`Failed to reconnect session ${sessionId}:`, error);
            
            const session = this.sessions.get(sessionId);
            if (session) {
                session.status = 'auth_required';
                
                if (this.mainWindow && this.mainWindow.webContents) {
                    this.mainWindow.webContents.send('session-auth-required', {
                        sessionId,
                        userInfo: session.userInfo,
                        message: `فشل في إعادة الاتصال: ${error.message}`,
                        canReconnect: true
                    });
                }
            }
        }
    }

    /**
     * بدء مراقبة الجلسات النشطة
     */
    startSessionMonitoring() {
        // إيقاف المراقبة السابقة إن وجدت
        if (this.sessionMonitorInterval) {
            clearInterval(this.sessionMonitorInterval);
        }

        console.log('Starting session monitoring...');
        
        // مراقبة كل 30 ثانية
        this.sessionMonitorInterval = setInterval(() => {
            this.monitorActiveSessions();
        }, 30000);
        
        // مراقبة فورية
        this.monitorActiveSessions();
    }

    /**
     * مراقبة الجلسات النشطة
     */
    async monitorActiveSessions() {
        const activeSessions = Array.from(this.sessions.values()).filter(
            session => session.client && session.status === 'ready'
        );

        for (const session of activeSessions) {
            try {
                // فحص حالة العميل
                if (session.client && session.client.info) {
                    const state = await session.client.getState();
                    
                    if (state !== 'CONNECTED') {
                        console.log(`Session ${session.id} is not connected (state: ${state})`);
                        
                        // تحديث حالة الجلسة
                        session.status = 'disconnected';
                        session.lastDisconnection = new Date().toISOString();
                        this.saveSession(session.id);
                        
                        // إشعار الواجهة الأمامية
                        if (this.mainWindow && this.mainWindow.webContents) {
                            this.mainWindow.webContents.send('session-disconnected', {
                                sessionId: session.id,
                                userInfo: session.userInfo,
                                message: 'تم قطع الاتصال بالجلسة. قد يكون بسبب تسجيل الخروج من الهاتف.'
                            });
                        }
                        
                        // محاولة إعادة الاتصال التلقائي بعد 5 ثوان
                        setTimeout(async () => {
                            try {
                                await this.attemptSessionReconnection(session.id);
                            } catch (error) {
                                console.error(`Auto-reconnection failed for ${session.id}:`, error);
                            }
                        }, 5000);
                    }
                }
            } catch (error) {
                console.error(`Error monitoring session ${session.id}:`, error);
            }
        }
    }

    /**
     * إيقاف مراقبة الجلسات
     */
    stopSessionMonitoring() {
        if (this.sessionMonitorInterval) {
            clearInterval(this.sessionMonitorInterval);
            this.sessionMonitorInterval = null;
            console.log('Session monitoring stopped');
        }
    }

    /**
     * الاتصال بجلسة محفوظة (LocalAuth يستعيد الجلسة تلقائياً)
     */
    async connectToSavedSession(sessionId) {
        const sessionData = this.sessions.get(sessionId);
        if (!sessionData) {
            throw new Error(`Session ${sessionId} not found`);
        }

        console.log(`Initializing client for saved session: ${sessionId}`);

        // التأكد من وجود مجلد الجلسة
        const sessionPath = path.join(this.sessionsPath, sessionId);
        this.ensureDirectoryExists(sessionPath);
        
        // التحقق من وجود ملفات الجلسة المحفوظة
        const sessionInfoPath = path.join(sessionPath, 'session-info.json');
        const sessionFilesExist = fs.existsSync(path.join(sessionPath, 'session.json')) || 
                                 fs.existsSync(path.join(sessionPath, 'Default'));
        
        if (!sessionFilesExist) {
            console.log(`No saved session files found for ${sessionId}, this might be a new session`);
            // إذا لم توجد ملفات محفوظة، قد تحتاج الجلسة إلى QR code
            sessionData.connectionCount = 0;
        }

        // تحديث معلومات الجلسة من الملف المحفوظ إذا وجد
        if (fs.existsSync(sessionInfoPath)) {
            try {
                const savedInfo = JSON.parse(fs.readFileSync(sessionInfoPath, 'utf8'));
                // دمج المعلومات المحفوظة مع البيانات الحالية
                Object.assign(sessionData, savedInfo, {
                    client: sessionData.client, // الحفاظ على العميل الحالي
                    status: 'connecting' // تحديث الحالة للاتصال
                });
                console.log(`Loaded saved session info for ${sessionId}`);
            } catch (error) {
                console.warn(`Could not load session info for ${sessionId}:`, error.message);
            }
        }

        // إنشاء عميل جديد - LocalAuth سيستعيد الجلسة المحفوظة تلقائياً
        const client = new Client({
            authStrategy: new LocalAuth({
                clientId: sessionId,
                dataPath: sessionPath
            }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-default-apps',
                    '--disable-component-extensions-with-background-pages',
                    '--disable-background-networking',
                    '--disable-sync',
                    '--metrics-recording-only',
                    '--no-default-browser-check',
                    '--no-pings',
                    '--password-store=basic',
                    '--use-mock-keychain',
                    '--disable-logging',
                    '--disable-dev-tools',
                    '--disable-hang-monitor'
                ],
                executablePath: undefined,
                timeout: 60000,
                handleSIGINT: false,
                handleSIGTERM: false,
                handleSIGHUP: false
            },
            qrMaxRetries: 0, // لا نريد QR للجلسات المحفوظة
            restartOnAuthFail: false, // لا نريد إعادة تشغيل للجلسات المحفوظة
            takeoverOnConflict: true,
            takeoverTimeoutMs: 45000,
            authTimeoutMs: 60000, // وقت محسن للجلسات المحفوظة
            webVersionCache: {
                type: 'remote',
                remotePath: 'https://raw.githubusercontent.com/wppconnect-team/wa-version/main/html/2.2412.54.html'
            }
        });

        // تحديث بيانات الجلسة
        sessionData.client = client;
        sessionData.status = 'connecting';
        sessionData.lastUpdated = new Date().toISOString();

        // حفظ العميل في الخريطة
        this.clients.set(sessionId, client);

        // إعداد مستمعي الأحداث
        this.setupClientEvents(sessionId, client);

        // بدء العميل بشكل غير متزامن - LocalAuth سيستعيد الجلسة المحفوظة
        console.log(`Starting client initialization for saved session: ${sessionId}`);
        setImmediate(async () => {
            try {
                await client.initialize();
            } catch (error) {
                console.error(`Failed to initialize saved session ${sessionId}:`, error);
                this.mainWindow.webContents.send('session-disconnected', {
                    sessionId,
                    error: `فشل في تهيئة الجلسة المحفوظة: ${error.message}`
                });
            }
        });

        return { success: true, sessionId };
    }

    /**
     * إعادة الاتصال بجلسة محفوظة محددة (للاستخدام اليدوي)
     */
    async reconnectSavedSession(sessionId) {
        const sessionData = this.sessions.get(sessionId);
        if (!sessionData) {
            throw new Error(`Session ${sessionId} not found`);
        }

        console.log(`Reconnecting to saved session: ${sessionId}`);

        // التأكد من وجود مجلد الجلسة
        const sessionPath = path.join(this.sessionsPath, sessionId);
        this.ensureDirectoryExists(sessionPath);

        // إنشاء عميل جديد بنفس الإعدادات
        const client = new Client({
            authStrategy: new LocalAuth({
                clientId: sessionId,
                dataPath: sessionPath
            }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-default-apps',
                    '--disable-component-extensions-with-background-pages',
                    '--disable-background-networking',
                    '--disable-sync',
                    '--metrics-recording-only',
                    '--no-default-browser-check',
                    '--no-pings',
                    '--password-store=basic',
                    '--use-mock-keychain',
                    '--disable-logging',
                    '--disable-dev-tools',
                    '--disable-hang-monitor'
                ],
                executablePath: undefined,
                timeout: 45000,
                handleSIGINT: false,
                handleSIGTERM: false,
                handleSIGHUP: false
            },
            qrMaxRetries: 2, // أقل للجلسات المحفوظة
            restartOnAuthFail: true,
            takeoverOnConflict: true,
            takeoverTimeoutMs: 30000,
            authTimeoutMs: 30000,
            qrTimeoutMs: 20000, // أقل للجلسات المحفوظة
            webVersionCache: {
                type: 'remote',
                remotePath: 'https://raw.githubusercontent.com/wppconnect-team/wa-version/main/html/2.2412.54.html'
            }
        });

        // تحديث بيانات الجلسة
        sessionData.client = client;
        sessionData.status = 'reconnecting';
        sessionData.lastUpdated = new Date().toISOString();

        // حفظ العميل في الخريطة
        this.clients.set(sessionId, client);

        // إعداد مستمعي الأحداث
        this.setupClientEvents(sessionId, client);

        // بدء العميل بشكل غير متزامن لتحسين الأداء
        setImmediate(async () => {
            try {
                await client.initialize();
            } catch (error) {
                console.error(`Failed to initialize reconnected session ${sessionId}:`, error);
                this.mainWindow.webContents.send('session-disconnected', {
                    sessionId,
                    error: `فشل في إعادة تهيئة الجلسة: ${error.message}`
                });
            }
        });

        return { success: true, sessionId };
    }

    /**
     * محاولة استرداد الجلسات من ملف احتياطي
     */
    tryRestoreFromBackup() {
        try {
            const files = fs.readdirSync(this.sessionsPath);
            const backupFiles = files
                .filter(file => file.startsWith('sessions_backup_') && file.endsWith('.json'))
                .map(file => ({
                    name: file,
                    path: path.join(this.sessionsPath, file),
                    time: fs.statSync(path.join(this.sessionsPath, file)).mtime
                }))
                .sort((a, b) => b.time - a.time); // الأحدث أولاً

            if (backupFiles.length > 0) {
                console.log(`Attempting to restore from backup: ${backupFiles[0].name}`);

                // نسخ الملف الاحتياطي كملف رئيسي
                fs.copyFileSync(backupFiles[0].path, this.sessionsFile);
                console.log('Successfully restored from backup');

                // إعادة تحميل الجلسات
                this.loadSavedSessions();
            } else {
                console.log('No backup files found, creating empty sessions file');
                this.updateSessionsIndex();
            }
        } catch (error) {
            console.error('Error restoring from backup:', error);
            // إنشاء ملف فارغ كحل أخير
            this.updateSessionsIndex();
        }
    }

    /**
     * حفظ معلومات جلسة واحدة في مجلدها الخاص
     */
    saveSessionInfo(sessionId, sessionData) {
        try {
            const sessionPath = path.join(this.sessionsPath, sessionId);
            const sessionInfoFile = path.join(sessionPath, 'session-info.json');
            
            // إنشاء مجلد الجلسة إذا لم يكن موجوداً
            if (!fs.existsSync(sessionPath)) {
                fs.mkdirSync(sessionPath, { recursive: true });
            }
            
            // معلومات الجلسة التي سيتم حفظها
            const sessionInfo = {
                id: sessionData.id,
                phoneNumber: sessionData.phoneNumber,
                connectionType: sessionData.connectionType,
                userInfo: sessionData.userInfo || null,
                status: sessionData.status === 'connected' ? 'saved' : sessionData.status,
                createdAt: sessionData.createdAt,
                lastConnected: sessionData.lastConnected,
                connectionCount: sessionData.connectionCount || 0,
                deviceInfo: sessionData.deviceInfo || null,
                whatsappState: sessionData.whatsappState || null,
                messageCount: sessionData.messageCount || 0,
                contactCount: sessionData.contactCount || 0,
                chatCount: sessionData.chatCount || 0,
                version: '2.0.0',
                lastUpdated: new Date().toISOString()
            };
            
            fs.writeFileSync(sessionInfoFile, JSON.stringify(sessionInfo, null, 2));
            console.log(`Session info saved for: ${sessionId}`);
            
        } catch (error) {
            console.error(`Error saving session info for ${sessionId}:`, error);
        }
    }
    
    /**
     * تحديث فهرس الجلسات الرئيسي (ملف بسيط للمراجعة السريعة)
     */
    updateSessionsIndex() {
        try {
            const activeSessions = [];
            
            // جمع الجلسات النشطة فقط
            for (const [sessionId, sessionData] of this.sessions) {
                if (sessionData.status === 'saved' || sessionData.status === 'connected') {
                    activeSessions.push({
                        id: sessionId,
                        phoneNumber: sessionData.phoneNumber,
                        status: sessionData.status,
                        lastConnected: sessionData.lastConnected,
                        connectionCount: sessionData.connectionCount || 0
                    });
                }
            }
            
            const indexData = {
                version: '2.0.0',
                timestamp: new Date().toISOString(),
                totalSessions: activeSessions.length,
                sessions: activeSessions
            };
            
            fs.writeFileSync(this.sessionsFile, JSON.stringify(indexData, null, 2));
            console.log(`Sessions index updated with ${activeSessions.length} active sessions`);
            
        } catch (error) {
            console.error('Error updating sessions index:', error);
        }
    }
    
    /**
     * حفظ جلسة واحدة (يستدعى عند تحديث الجلسة)
     */
    saveSession(sessionId) {
        try {
            const sessionData = this.sessions.get(sessionId);
            if (!sessionData) {
                console.warn(`Cannot save session ${sessionId}: not found`);
                return;
            }
            
            // حفظ معلومات الجلسة فقط إذا كانت متصلة أو محفوظة
            if (sessionData.status === 'connected' || sessionData.status === 'saved') {
                this.saveSessionInfo(sessionId, sessionData);
                this.updateSessionsIndex();
            }
            
        } catch (error) {
            console.error(`Error saving session ${sessionId}:`, error);
        }
    }
    
    /**
     * حفظ جميع الجلسات النشطة
     */
    saveSessions() {
        try {
            let savedCount = 0;
            
            for (const [sessionId, sessionData] of this.sessions) {
                // حفظ الجلسات المتصلة والمحفوظة فقط
                if (sessionData.status === 'connected' || sessionData.status === 'saved') {
                    this.saveSessionInfo(sessionId, sessionData);
                    savedCount++;
                } else {
                    console.log(`Skipping session ${sessionId} with status: ${sessionData.status}`);
                }
            }
            
            // تحديث الفهرس الرئيسي
            this.updateSessionsIndex();
            
            console.log(`Saved ${savedCount} active sessions`);
            
        } catch (error) {
            console.error('Error saving sessions:', error);
        }
    }

    /**
     * تنظيف الجلسات غير الصحيحة ومجلداتها - نسخة محسنة لحماية الجلسات المحفوظة
     */
    cleanupInvalidSessions() {
        try {
            console.log('Cleaning up invalid sessions (safe mode)...');
            let cleanedCount = 0;
            
            // البحث عن الجلسات غير الصحيحة في الذاكرة فقط
            // لا نحذف أي مجلدات تحتوي على ملفات مصادقة صالحة أو محمية
            const invalidSessions = [];
            for (const [sessionId, sessionData] of this.sessions) {
                // فقط الجلسات الجديدة التي فشلت في التهيئة ولا تحتوي على ملفات مصادقة
                if ((sessionData.status === 'qr_ready' ||
                     sessionData.status === 'connecting' ||
                     sessionData.status === 'initializing') &&
                    (!sessionData.connectionCount && !sessionData.lastConnected)) {
                    
                    const sessionPath = path.join(this.sessionsPath, sessionId);
                    
                    // التحقق من حماية الجلسة أولاً
                    if (this.isSessionProtected(sessionId)) {
                        console.log(`Preserving protected session ${sessionId}`);
                        sessionData.status = 'saved';
                        sessionData.lastUpdated = new Date().toISOString();
                        continue;
                    }
                    
                    // التحقق من عدم وجود ملفات مصادقة قبل الحذف
                    const hasAuthFiles = this.checkSessionAuthFiles(sessionPath, sessionId);
                    
                    if (!hasAuthFiles) {
                        invalidSessions.push(sessionId);
                    } else {
                        console.log(`Preserving session ${sessionId} - has authentication files`);
                        // تحديث حالة الجلسة للمحفوظة
                        sessionData.status = 'saved';
                        sessionData.lastUpdated = new Date().toISOString();
                    }
                }
            }
            
            // حذف الجلسات غير الصحيحة فقط من الذاكرة
            for (const sessionId of invalidSessions) {
                console.log(`Removing invalid session from memory: ${sessionId}`);
                this.sessions.delete(sessionId);
                
                // حذف مجلد الجلسة فقط إذا كان فارغاً تماماً
                const sessionPath = path.join(this.sessionsPath, sessionId);
                if (fs.existsSync(sessionPath)) {
                    try {
                        const files = fs.readdirSync(sessionPath);
                        // حذف فقط إذا كان المجلد فارغاً أو يحتوي على ملفات مؤقتة فقط
                        // تجاهل ملف الحماية عند فحص الملفات المؤقتة
                        const nonTempFiles = files.filter(file => 
                            !file.endsWith('.tmp') && 
                            !file.endsWith('.log') && 
                            file !== '.session-protected'
                        );
                        
                        if (nonTempFiles.length === 0) {
                            fs.rmSync(sessionPath, { recursive: true, force: true });
                            console.log(`Deleted empty session directory: ${sessionPath}`);
                            cleanedCount++;
                        } else {
                            console.log(`Preserving session directory ${sessionPath} - contains files:`, nonTempFiles);
                        }
                    } catch (dirError) {
                        console.error(`Error checking session directory ${sessionPath}:`, dirError);
                    }
                }
            }
            
            if (cleanedCount > 0) {
                console.log(`Cleaned up ${cleanedCount} empty session directories`);
                this.updateSessionsIndex();
            } else {
                console.log('No empty session directories found to clean up');
            }
            
        } catch (error) {
            console.error('Error during session cleanup:', error);
        }
    }

    /**
     * فحص وجود ملفات المصادقة في مجلد الجلسة
     */
    checkSessionAuthFiles(sessionPath, sessionId) {
        try {
            if (!fs.existsSync(sessionPath)) {
                return false;
            }

            // فحص ملفات المصادقة المختلفة
            const authFiles = [
                'session-info.json',
                '.wwebjs_auth',
                '.wwebjs_cache',
                'Default',
                'session.json'
            ];

            // فحص الملفات في المجلد الرئيسي
            for (const authFile of authFiles) {
                if (fs.existsSync(path.join(sessionPath, authFile))) {
                    return true;
                }
            }

            // فحص مجلد الجلسة الفرعي
            const subSessionPath = path.join(sessionPath, `session-${sessionId}`);
            if (fs.existsSync(subSessionPath)) {
                for (const authFile of authFiles) {
                    if (fs.existsSync(path.join(subSessionPath, authFile))) {
                        return true;
                    }
                }
            }

            // فحص أي ملفات تبدأ بـ session-
            const files = fs.readdirSync(sessionPath);
            return files.some(file => file.startsWith('session-') && 
                             (fs.statSync(path.join(sessionPath, file)).isDirectory() ||
                              file.endsWith('.json')));

        } catch (error) {
            console.error(`Error checking auth files for ${sessionPath}:`, error);
            return true; // في حالة الخطأ، نفترض وجود ملفات مهمة
        }
    }

    /**
     * تنظيف الملفات الاحتياطية القديمة
     */
    cleanupBackupFiles() {
        try {
            const files = fs.readdirSync(this.sessionsPath);
            const backupFiles = files
                .filter(file => file.startsWith('sessions_backup_') && file.endsWith('.json'))
                .map(file => ({
                    name: file,
                    path: path.join(this.sessionsPath, file),
                    time: fs.statSync(path.join(this.sessionsPath, file)).mtime
                }))
                .sort((a, b) => b.time - a.time); // ترتيب من الأحدث للأقدم

            // الاحتفاظ بآخر 5 نسخ احتياطية فقط
            if (backupFiles.length > 5) {
                const filesToDelete = backupFiles.slice(5);
                filesToDelete.forEach(file => {
                    try {
                        fs.unlinkSync(file.path);
                        console.log(`Deleted old backup file: ${file.name}`);
                    } catch (error) {
                        console.error(`Failed to delete backup file ${file.name}:`, error);
                    }
                });
            }
        } catch (error) {
            console.error('Error cleaning up backup files:', error);
        }
    }

    /**
     * إنشاء جلسة WhatsApp جديدة
     */
    async createSession(sessionId, connectionType = 'qr', phoneNumber = null) {
        try {
            console.log(`Creating WhatsApp session: ${sessionId}, type: ${connectionType}, phone: ${phoneNumber}`);

            // إنشاء مجلد للجلسة في مسار التطبيق
            const sessionPath = path.join(this.sessionsPath, sessionId);
            this.ensureDirectoryExists(sessionPath);

            // إعداد العميل بأحدث المعايير لعام 2025 مع تحسينات الأداء
            const client = new Client({
                authStrategy: new LocalAuth({
                    clientId: sessionId,
                    dataPath: sessionPath
                }),
                puppeteer: {
                    headless: true,
                    args: [
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--disable-gpu',
                        '--disable-background-timer-throttling',
                        '--disable-backgrounding-occluded-windows',
                        '--disable-renderer-backgrounding',
                        '--disable-features=TranslateUI',
                        '--disable-ipc-flooding-protection',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--disable-blink-features=AutomationControlled',
                        '--disable-extensions',
                        '--disable-plugins',
                        '--disable-default-apps',
                        '--disable-component-extensions-with-background-pages',
                        '--disable-background-networking',
                        '--disable-sync',
                        '--metrics-recording-only',
                        '--no-default-browser-check',
                        '--no-pings',
                        '--password-store=basic',
                        '--use-mock-keychain',
                        '--disable-logging',
                        '--disable-dev-tools',
                        '--disable-hang-monitor'
                    ],
                    executablePath: undefined, // استخدام Chromium المدمج
                    timeout: 45000, // تقليل timeout لتسريع الاستجابة
                    handleSIGINT: false,
                    handleSIGTERM: false,
                    handleSIGHUP: false
                },
                qrMaxRetries: 5,
                restartOnAuthFail: true, // إعادة التشغيل عند فشل المصادقة
                takeoverOnConflict: true, // ميزة جديدة لحل تضارب الجلسات
                takeoverTimeoutMs: 30000, // تقليل timeout لحل التضارب
                authTimeoutMs: 30000, // تقليل timeout للمصادقة
                qrTimeoutMs: 30000, // تقليل timeout لـ QR code
                webVersionCache: {
                    type: 'remote',
                    remotePath: 'https://raw.githubusercontent.com/wppconnect-team/wa-version/main/html/2.2412.54.html'
                }
            });

            // حفظ بيانات الجلسة
            const sessionData = {
                id: sessionId,
                connectionType,
                phoneNumber,
                status: 'initializing',
                client,
                createdAt: new Date().toISOString(),
                lastUpdated: new Date().toISOString(),
                sessionPath: sessionPath,
                version: '1.31.0' // إصدار المكتبة
            };

            this.clients.set(sessionId, client);
            this.sessions.set(sessionId, sessionData);

            // حفظ الجلسة فور الإنشاء (قبل التهيئة)
            await this.saveSession(sessionId);

            // إعداد مستمعي الأحداث
            this.setupClientEvents(sessionId, client);

            // بدء العميل بشكل غير متزامن لتسريع الاستجابة
            console.log(`Initializing WhatsApp client for session: ${sessionId}`);
            setImmediate(async () => {
                try {
                    await client.initialize();
                    console.log(`WhatsApp session ${sessionId} initialized successfully`);
                    
                    // حفظ الجلسة مرة أخرى بعد التهيئة الناجحة
                    sessionData.status = 'initialized';
                    sessionData.lastUpdated = new Date().toISOString();
                    await this.saveSession(sessionId);
                } catch (initError) {
                    console.error(`Failed to initialize session ${sessionId}:`, initError);
                    this.cleanupSession(sessionId);
                }
            });

            return { success: true, sessionId };

        } catch (error) {
            console.error(`Failed to create session ${sessionId}:`, error);
            this.cleanupSession(sessionId);
            return { success: false, error: error.message };
        }
    }

    /**
     * إعداد مستمعي أحداث العميل
     */
    setupClientEvents(sessionId, client) {
        const session = this.sessions.get(sessionId);

        // QR Code للاقتران مع تحسينات الأداء
        client.on('qr', async (qr) => {
            console.log(`QR Code generated for session: ${sessionId}`);
            
            // تجاهل QR code للجلسات المحفوظة التي يجب أن تتصل تلقائياً
            if (session.connectionCount && session.connectionCount > 0) {
                console.log(`Ignoring QR code for saved session: ${sessionId} (connectionCount: ${session.connectionCount})`);
                return;
            }
            
            session.status = 'qr_ready';

            try {
                if (session.connectionType === 'qr') {
                    // تحويل QR إلى صورة بشكل محسن وإرساله للواجهة الأمامية
                    setImmediate(async () => {
                        try {
                            const qrImage = await QRCode.toDataURL(qr, {
                                width: 256,
                                margin: 1,
                                color: {
                                    dark: '#000000',
                                    light: '#FFFFFF'
                                },
                                errorCorrectionLevel: 'M',
                                type: 'image/png',
                                quality: 0.92,
                                rendererOpts: {
                                    quality: 0.92
                                }
                            });
                            
                            this.mainWindow.webContents.send('qr-code-generated', {
                                sessionId,
                                qrCode: qrImage
                            });
                            console.log(`QR code sent to frontend for session: ${sessionId}`);
                        } catch (qrError) {
                            console.error(`Failed to generate QR image for ${sessionId}:`, qrError);
                        }
                    });
                } else if (session.connectionType === 'pairing' && session.phoneNumber) {
                    // طلب رمز الاقتران
                    console.log(`Requesting pairing code for session: ${sessionId}, phone: ${session.phoneNumber}`);

                    // تنظيف رقم الهاتف
                    let cleanPhone = session.phoneNumber.replace(/[\s\-\(\)+]/g, '');
                    if (cleanPhone.startsWith('+')) {
                        cleanPhone = cleanPhone.substring(1);
                    }

                    try {
                        const pairingCode = await client.requestPairingCode(cleanPhone, true);
                        console.log(`Pairing code received: ${pairingCode}`);

                        session.status = 'pairing_requested';
                        session.pairingCode = pairingCode;
                        session.pairingRequestTime = new Date().toISOString();

                        // إرسال رمز الاقتران للواجهة الأمامية
                        this.mainWindow.webContents.send('pairing-code-generated', {
                            sessionId,
                            code: pairingCode
                        });

                    } catch (pairingError) {
                        console.error(`Failed to request pairing code for ${sessionId}:`, pairingError);
                        session.status = 'error';
                        this.mainWindow.webContents.send('session-disconnected', {
                            sessionId,
                            error: `فشل في طلب رمز الاقتران: ${pairingError.message}`
                        });
                    }
                }
            } catch (error) {
                console.error(`Failed to handle QR/Pairing for ${sessionId}:`, error);
            }
        });

        // العميل جاهز
        client.on('ready', async () => {
            console.log(`WhatsApp client ready for session: ${sessionId}`);
            session.status = 'connected';

            try {
                // الحصول على معلومات المستخدم المحدثة من WhatsApp
                const info = client.info;
                
                // جلب صورة الملف الشخصي
                let profilePicture = null;
                try {
                    profilePicture = await client.getProfilePicUrl(info.wid._serialized);
                    console.log(`Profile picture loaded for session ${sessionId}`);
                } catch (profileError) {
                    console.warn(`Could not load profile picture for session ${sessionId}:`, profileError.message);
                }
                
                const updatedUserInfo = {
                    id: info.wid._serialized,
                    name: info.pushname || 'مستخدم WhatsApp',
                    phone: info.wid.user,
                    platform: info.platform || 'unknown',
                    battery: info.battery || null,
                    plugged: info.plugged || false,
                    profilePicture: profilePicture
                };

                // مقارنة البيانات القديمة بالجديدة
                const oldUserInfo = session.userInfo;
                const hasChanges = !oldUserInfo ||
                    JSON.stringify(oldUserInfo) !== JSON.stringify(updatedUserInfo);

                if (hasChanges) {
                    console.log(`User info updated for session ${sessionId}:`, updatedUserInfo);
                }

                // تحديث بيانات الجلسة بالمعلومات الجديدة من WhatsApp
                session.userInfo = updatedUserInfo;
                session.lastConnected = new Date().toISOString();
                session.lastUpdated = new Date().toISOString();
                session.isReady = true;
                session.connectionCount = (session.connectionCount || 0) + 1;

                // جلب معلومات إضافية من WhatsApp
                try {
                    // جلب حالة المستخدم
                    const state = await client.getState();
                    session.whatsappState = state;

                    // جلب معلومات الجهاز (معطل مؤقتًا)
                    // const hostDevice = await client.getHostDevice();
                    // if (hostDevice) {
                    //     session.deviceInfo = {
                    //         manufacturer: hostDevice.manufacturer,
                    //         model: hostDevice.model,
                    //         osVersion: hostDevice.os_version,
                    //         waVersion: hostDevice.wa_version
                    //     };
                    // }

                    // جلب إحصائيات الرسائل وجهات الاتصال
                    try {
                        const chats = await client.getChats();
                        const contacts = await client.getContacts();
                        
                        // حساب عدد الرسائل من جميع المحادثات
                        let totalMessages = 0;
                        for (const chat of chats) {
                            if (chat.unreadCount) {
                                totalMessages += chat.unreadCount;
                            }
                        }

                        // حفظ الإحصائيات في الجلسة
                        session.messageCount = totalMessages;
                        session.contactCount = contacts.length;
                        session.chatCount = chats.length;
                        
                        console.log(`Statistics loaded for session ${sessionId}: ${totalMessages} messages, ${contacts.length} contacts, ${chats.length} chats`);
                    } catch (statsError) {
                        console.warn(`Could not load statistics for session ${sessionId}:`, statsError.message);
                        // تعيين قيم افتراضية في حالة الفشل
                        session.messageCount = 0;
                        session.contactCount = 0;
                        session.chatCount = 0;
                    }

                    console.log(`Extended info loaded for session ${sessionId}`);
                } catch (extendedInfoError) {
                    console.warn(`Could not load extended info for session ${sessionId}:`, extendedInfoError.message);
                    // تعيين قيم افتراضية في حالة الفشل
                    session.messageCount = 0;
                    session.contactCount = 0;
                    session.chatCount = 0;
                }

                // حفظ الجلسة مع المعلومات المحدثة من WhatsApp
                this.saveSession(sessionId);



                console.log(`Session ${sessionId} connected successfully with updated data from WhatsApp`);

                // إرسال إشعار للواجهة الأمامية مع البيانات المحدثة
                if (this.mainWindow && this.mainWindow.webContents) {
                    this.mainWindow.webContents.send('session-connected', {
                        sessionId,
                        userInfo: session.userInfo,
                        deviceInfo: session.deviceInfo,
                        whatsappState: session.whatsappState,
                        timestamp: session.lastConnected,
                        connectionCount: session.connectionCount,
                        messageCount: session.messageCount || 0,
                        contactCount: session.contactCount || 0,
                        chatCount: session.chatCount || 0,
                        hasChanges
                    });
                }
            } catch (error) {
                console.error(`Error handling ready event for session ${sessionId}:`, error);
                // حفظ الجلسة حتى لو فشل في الحصول على معلومات المستخدم
                session.lastConnected = new Date().toISOString();
                session.lastUpdated = new Date().toISOString();
                session.connectionCount = (session.connectionCount || 0) + 1;
                session.lastError = error.message;
                this.saveSession(sessionId);
            }
        });

        // رسالة جديدة
        client.on('message', async (message) => {
            console.log(`New message in session ${sessionId}:`, message.body);
            
            // إرسال الرسالة للواجهة الأمامية
            this.mainWindow.webContents.send('message-received', {
                sessionId,
                message: {
                    id: message.id._serialized,
                    body: message.body,
                    from: message.from,
                    to: message.to,
                    timestamp: message.timestamp,
                    type: message.type,
                    isGroupMsg: message.isGroupMsg
                }
            });
        });

        // انقطاع الاتصال
        client.on('disconnected', (reason) => {
            console.log(`Session ${sessionId} disconnected:`, reason);
            session.status = 'disconnected';

            // حفظ حالة الانقطاع
            this.saveSession(sessionId);



            if (this.mainWindow && this.mainWindow.webContents) {
                this.mainWindow.webContents.send('session-disconnected', {
                    sessionId,
                    reason
                });
            }
        });

        // خطأ في المصادقة
        client.on('auth_failure', (message) => {
            console.error(`Authentication failed for session ${sessionId}:`, message);
            session.status = 'auth_failed';

            // للجلسات المحفوظة، لا نحذف البيانات فوراً
            if (session.connectionCount && session.connectionCount > 0) {
                console.log(`Saved session ${sessionId} auth failed, keeping data for retry`);
                session.lastError = `Authentication failed: ${message}`;
                session.lastUpdated = new Date().toISOString();
                this.saveSession(sessionId);
            } else {
                // جلسة جديدة فاشلة، يمكن حذفها
                this.cleanupSession(sessionId);
            }

            if (this.mainWindow && this.mainWindow.webContents) {
                this.mainWindow.webContents.send('session-auth-failed', {
                    sessionId,
                    message,
                    canRetry: session.connectionCount && session.connectionCount > 0
                });
            }
        });

        // تحديث حالة التحميل
        client.on('loading_screen', (percent, message) => {
            console.log(`Loading ${percent}% for session ${sessionId}: ${message}`);

            if (this.mainWindow && this.mainWindow.webContents) {
                this.mainWindow.webContents.send('session-loading', {
                    sessionId,
                    percent,
                    message
                });
            }
        });

        // معالجة أخطاء العميل
        client.on('error', (error) => {
            console.error(`Client error for session ${sessionId}:`, error);
            session.status = 'error';

            if (this.mainWindow && this.mainWindow.webContents) {
                this.mainWindow.webContents.send('session-error', {
                    sessionId,
                    error: error.message
                });
            }
        });
    }



    /**
     * إعادة الاتصال بجلسة محفوظة (للاستخدام اليدوي من الواجهة)
     */
    async reconnectSession(sessionId) {
        try {
            const session = this.sessions.get(sessionId);
            if (!session) {
                throw new Error('Session not found');
            }

            console.log(`Manual reconnection to saved session: ${sessionId}`);

            // استخدام دالة الاتصال بالجلسة المحفوظة
            const result = await this.connectToSavedSession(sessionId);

            if (result.success) {
                console.log(`Successfully reconnected to session: ${sessionId}`);
                return { success: true, message: 'تم الاتصال بنجاح' };
            } else {
                throw new Error(result.error || 'Failed to connect to saved session');
            }

        } catch (error) {
            console.error(`Failed to reconnect session ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * إرسال رسالة
     */
    async sendMessage(sessionId, to, message) {
        try {
            const client = this.clients.get(sessionId);
            if (!client) {
                throw new Error('Session not found');
            }

            const result = await client.sendMessage(to, message);
            console.log(`Message sent from session ${sessionId} to ${to}`);
            
            return { success: true, messageId: result.id._serialized };
        } catch (error) {
            console.error(`Failed to send message from session ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * إرسال ملف
     */
    async sendMedia(sessionId, to, mediaPath, caption = '') {
        try {
            const client = this.clients.get(sessionId);
            if (!client) {
                throw new Error('Session not found');
            }

            const media = MessageMedia.fromFilePath(mediaPath);
            const result = await client.sendMessage(to, media, { caption });
            
            console.log(`Media sent from session ${sessionId} to ${to}`);
            return { success: true, messageId: result.id._serialized };
        } catch (error) {
            console.error(`Failed to send media from session ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * الحصول على معلومات جهة الاتصال
     */
    async getContactInfo(sessionId, contactId) {
        try {
            const client = this.clients.get(sessionId);
            if (!client) {
                throw new Error('Session not found');
            }

            const contact = await client.getContactById(contactId);
            return {
                success: true,
                contact: {
                    id: contact.id._serialized,
                    name: contact.name,
                    pushname: contact.pushname,
                    isGroup: contact.isGroup,
                    isUser: contact.isUser,
                    isWAContact: contact.isWAContact
                }
            };
        } catch (error) {
            console.error(`Failed to get contact info for session ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * الحصول على جميع جهات الاتصال مع التفاصيل الكاملة - نسخة محسنة مع التخزين المؤقت
     */
    async getContacts(sessionId, useCache = true, progressCallback = null) {
        try {
            const client = this.clients.get(sessionId);
            if (!client) {
                throw new Error('Session not found');
            }

            // محاولة تحميل جهات الاتصال من التخزين المؤقت أولاً
            if (useCache) {
                const cachedData = await this.contactsCache.loadContacts(sessionId);
                if (cachedData.success) {
                    console.log(`Loaded ${cachedData.contacts.length} contacts from cache for session ${sessionId}`);
                    
                    // إرسال البيانات المحفوظة فوراً
                    if (progressCallback) {
                        progressCallback({
                            type: 'cached_data',
                            contacts: cachedData.contacts,
                            total: cachedData.contacts.length,
                            fromCache: true
                        });
                    }
                    
                    // تحديث البيانات في الخلفية
                    this.updateContactsInBackground(sessionId, progressCallback);
                    
                    return {
                        success: true,
                        contacts: cachedData.contacts,
                        fromCache: true,
                        lastUpdated: cachedData.lastUpdated
                    };
                }
            }

            // إذا لم توجد بيانات محفوظة، تحميل جهات الاتصال من WhatsApp
            return await this.fetchContactsFromWhatsApp(sessionId, progressCallback);
            
        } catch (error) {
            console.error(`Failed to get contacts for session ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * تحميل جهات الاتصال من WhatsApp مباشرة
     */
    async fetchContactsFromWhatsApp(sessionId, progressCallback = null) {
        try {
            const client = this.clients.get(sessionId);
            if (!client) {
                throw new Error('Session not found');
            }

            console.log(`Fetching contacts from WhatsApp for session ${sessionId}`);
            
            if (progressCallback) {
                progressCallback({
                    type: 'loading_start',
                    message: 'جاري تحميل جهات الاتصال من WhatsApp...'
                });
            }

            const contacts = await client.getContacts();
            const contactsWithDetails = [];
            let processedCount = 0;
            
            // تصفية جهات الاتصال أولاً - فقط الجهات التي تنتهي بـ @c.us
            const validContacts = contacts.filter(contact => {
                // تجاهل المجموعات
                if (contact.isGroup || !contact.isUser) return false;
                
                // التحقق من أن ID ينتهي بـ @c.us فقط (تجاهل @lid)
                const contactId = contact.id?._serialized || contact.id;
                if (!contactId || !contactId.endsWith('@c.us')) {
                    console.debug(`Skipping contact with invalid ID format: ${contactId}`);
                    return false;
                }
                
                const phoneNumber = contact.number || contact.id?.user;
                if (!phoneNumber || phoneNumber === contactId) return false;
                
                const cleanNumber = phoneNumber.replace(/[^\d]/g, '');
                return cleanNumber.length >= 10;
            });

            console.log(`Found ${validContacts.length} valid contacts to process`);
            
            if (progressCallback) {
                progressCallback({
                    type: 'contacts_filtered',
                    total: validContacts.length,
                    message: `تم العثور على ${validContacts.length} جهة اتصال`
                });
            }
            
            // معالجة جهات الاتصال بدون صور أولاً (سريع)
            for (const contact of validContacts) {
                try {
                    const phoneNumber = contact.number || contact.id?.user;
                    
                    // استخراج الاسم المحفوظ بأفضل طريقة ممكنة
                    const savedName = contact.name || contact.shortName || null;
                    const displayName = contact.pushname || contact.verifiedName || null;
                    
                    const contactData = {
                        id: contact.id._serialized,
                        name: savedName, // الاسم المحفوظ في جهات الاتصال
                        pushname: displayName, // الاسم المعروض في WhatsApp
                        shortName: contact.shortName || null, // النسخة المختصرة من الاسم
                        verifiedName: contact.verifiedName || null, // الاسم المؤكد للحسابات التجارية
                        number: phoneNumber,
                        isWAContact: contact.isWAContact !== false,
                        isUser: contact.isUser,
                        isGroup: contact.isGroup,
                        isMyContact: contact.isMyContact || false, // هل محفوظ في جهات الاتصال
                        profilePicUrl: null // سيتم تحديثها لاحقاً
                    };
                    
                    contactsWithDetails.push(contactData);
                    processedCount++;
                    
                    // إرسال تحديث كل 10 جهات اتصال
                    if (processedCount % 10 === 0 && progressCallback) {
                        progressCallback({
                            type: 'contacts_processed',
                            processed: processedCount,
                            total: validContacts.length,
                            contacts: [...contactsWithDetails],
                            message: `تم معالجة ${processedCount} من ${validContacts.length} جهة اتصال`
                        });
                    }
                    
                } catch (contactError) {
                    console.debug(`Error processing contact ${contact.id?._serialized}:`, contactError.message);
                    continue;
                }
            }
            
            // حفظ البيانات الأساسية في التخزين المؤقت
            await this.contactsCache.saveContacts(sessionId, contactsWithDetails);
            
            if (progressCallback) {
                progressCallback({
                    type: 'basic_data_complete',
                    contacts: contactsWithDetails,
                    total: contactsWithDetails.length,
                    message: `تم تحميل ${contactsWithDetails.length} جهة اتصال`
                });
            }
            
            // تحميل صور الملفات الشخصية في الخلفية
            this.loadProfilePicturesInBackground(sessionId, contactsWithDetails, progressCallback);
            
            console.log(`Retrieved ${contactsWithDetails.length} contacts for session ${sessionId}`);
            
            return {
                success: true,
                contacts: contactsWithDetails,
                fromCache: false
            };
            
        } catch (error) {
            console.error(`Failed to fetch contacts from WhatsApp for session ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * تحديث جهات الاتصال في الخلفية
     */
    async updateContactsInBackground(sessionId, progressCallback = null) {
        try {
            console.log(`Updating contacts in background for session ${sessionId}`);
            
            // تحميل البيانات الجديدة
            const freshData = await this.fetchContactsFromWhatsApp(sessionId, progressCallback);
            
            if (freshData.success) {
                // مقارنة البيانات وإرسال التحديثات إذا كانت مختلفة
                const cachedData = await this.contactsCache.loadContacts(sessionId);
                
                if (cachedData.success) {
                    const hasChanges = JSON.stringify(cachedData.contacts) !== JSON.stringify(freshData.contacts);
                    
                    if (hasChanges && progressCallback) {
                        progressCallback({
                            type: 'data_updated',
                            contacts: freshData.contacts,
                            message: 'تم تحديث بيانات جهات الاتصال'
                        });
                    }
                }
            }
        } catch (error) {
            console.error(`Failed to update contacts in background for session ${sessionId}:`, error);
        }
    }

    /**
     * تحميل صور الملفات الشخصية في الخلفية
     */
    async loadProfilePicturesInBackground(sessionId, contacts, progressCallback = null) {
        try {
            const client = this.clients.get(sessionId);
            if (!client) return;

            console.log(`Loading profile pictures in background for ${contacts.length} contacts`);
            
            let picturesLoaded = 0;
            const batchSize = 5; // تحميل 5 صور في المرة الواحدة
            
            for (let i = 0; i < contacts.length; i += batchSize) {
                const batch = contacts.slice(i, i + batchSize);
                
                await Promise.all(batch.map(async (contact) => {
                    try {
                        const profilePicUrl = await client.getProfilePicUrl(contact.id);
                        contact.profilePicUrl = profilePicUrl;
                        picturesLoaded++;
                    } catch (picError) {
                        // تجاهل أخطاء الصورة
                        console.debug(`No profile picture for ${contact.id}`);
                    }
                }));
                
                // حفظ التحديث في التخزين المؤقت
                await this.contactsCache.saveContacts(sessionId, contacts);
                
                // إرسال تحديث للواجهة الأمامية
                if (progressCallback) {
                    progressCallback({
                        type: 'pictures_loaded',
                        picturesLoaded,
                        totalContacts: contacts.length,
                        contacts: [...contacts],
                        message: `تم تحميل ${picturesLoaded} صورة من ${contacts.length}`
                    });
                }
                
                // توقف قصير لتجنب إرهاق الخادم
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            console.log(`Completed loading profile pictures for session ${sessionId}`);
            
        } catch (error) {
            console.error(`Failed to load profile pictures for session ${sessionId}:`, error);
        }
    }

    /**
     * حذف جلسة
     */
    async deleteSession(sessionId) {
        try {
            console.log(`Deleting session: ${sessionId}`);
            
            const client = this.clients.get(sessionId);
            if (client) {
                await client.logout();
                await client.destroy();
            }

            // حذف كامل للجلسة مع الملفات (forceDelete = true)
            this.cleanupSession(sessionId, true);

            // تحديث فهرس الجلسات
            this.updateSessionsIndex();

            console.log(`Session ${sessionId} deleted successfully`);
            return { success: true };
        } catch (error) {
            console.error(`Failed to delete session ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * حذف حساب بالكامل - تجاهل جميع آليات الحماية
     */
    async forceDeleteAccount(sessionId) {
        try {
            console.log(`Force deleting account: ${sessionId}`);
            
            // تسجيل الخروج وإغلاق العميل إذا كان متصلاً
            const client = this.clients.get(sessionId);
            if (client) {
                try {
                    console.log(`Logging out from WhatsApp for session: ${sessionId}`);
                    await client.logout();
                    await client.destroy();
                } catch (logoutError) {
                    console.warn(`Error during logout for ${sessionId}:`, logoutError.message);
                    // المتابعة حتى لو فشل تسجيل الخروج
                }
            }

            // إزالة من خريطة العملاء
            this.clients.delete(sessionId);
            
            // حذف من الذاكرة
            this.sessions.delete(sessionId);
            
            // حذف جميع ملفات ومجلدات الجلسة بالقوة
            const sessionPath = path.join(this.sessionsPath, sessionId);
            if (fs.existsSync(sessionPath)) {
                try {
                    // حذف ملف الحماية أولاً إذا كان موجوداً
                    const protectionFile = path.join(sessionPath, '.session-protected');
                    if (fs.existsSync(protectionFile)) {
                        fs.unlinkSync(protectionFile);
                        console.log(`Removed protection file for session: ${sessionId}`);
                    }
                    
                    // حذف المجلد بالكامل مع جميع محتوياته
                    fs.rmSync(sessionPath, { recursive: true, force: true });
                    console.log(`✅ Force deleted session directory: ${sessionPath}`);
                } catch (deleteError) {
                    console.error(`Failed to delete session directory ${sessionPath}:`, deleteError);
                    throw deleteError;
                }
            }

            // تحديث فهرس الجلسات
            this.updateSessionsIndex();

            console.log(`Account ${sessionId} force deleted successfully`);
            return { success: true, message: 'تم حذف الحساب وجميع بياناته بنجاح' };
        } catch (error) {
            console.error(`Failed to force delete account ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * تنظيف الجلسة - نسخة محسنة مع حماية قوية للجلسات المحفوظة
     */
    cleanupSession(sessionId, forceDelete = false) {
        try {
            console.log(`Cleaning up session ${sessionId}, forceDelete: ${forceDelete}`);
            
            // إغلاق العميل إذا كان موجوداً
            const client = this.clients.get(sessionId);
            if (client) {
                try {
                    client.destroy();
                } catch (error) {
                    console.error(`Error destroying client for session ${sessionId}:`, error);
                }
            }

            // إزالة من خريطة العملاء
            this.clients.delete(sessionId);
            
            // التحقق من نوع التنظيف المطلوب
            const session = this.sessions.get(sessionId);
            const sessionPath = path.join(this.sessionsPath, sessionId);
            
            // فحص الحماية أولاً
            const isProtected = this.isSessionProtected(sessionId);
            const hasAuthFiles = this.checkSessionAuthFiles(sessionPath, sessionId);
            const isSavedSession = session && (session.connectionCount > 0 || session.lastConnected);
            
            // منع الحذف للجلسات المحمية
            if (isProtected && forceDelete) {
                console.log(`⚠️ Session ${sessionId} is protected - preventing deletion`);
                forceDelete = false; // إلغاء الحذف القسري للجلسات المحمية
            }
            
            // قرار الحذف مع حماية إضافية
            const shouldDeleteFiles = forceDelete && !hasAuthFiles && !isProtected;
            const shouldDeleteFromMemory = forceDelete || (!isSavedSession && !hasAuthFiles && !isProtected);
            
            if (shouldDeleteFiles) {
                // حذف ملفات الجلسة فقط في حالات محددة جداً
                if (fs.existsSync(sessionPath)) {
                    try {
                        // فحص نهائي قبل الحذف
                        const finalCheck = this.checkSessionAuthFiles(sessionPath, sessionId);
                        if (!finalCheck) {
                            fs.rmSync(sessionPath, { recursive: true, force: true });
                            console.log(`✅ Deleted unprotected session directory: ${sessionPath}`);
                        } else {
                            console.log(`🛡️ Final check prevented deletion of ${sessionId} - auth files found`);
                        }
                    } catch (error) {
                        console.error(`Failed to remove session directory ${sessionPath}:`, error);
                    }
                }
            } else {
                if (hasAuthFiles || isProtected) {
                    console.log(`🛡️ Preserving session ${sessionId} - protected or has authentication files`);
                } else {
                    console.log(`📁 Keeping session ${sessionId} - saved session`);
                }
            }
            
            if (shouldDeleteFromMemory) {
                // حذف من الذاكرة فقط للجلسات غير المحمية
                this.sessions.delete(sessionId);
                console.log(`🗑️ Removed session ${sessionId} from memory`);
            } else {
                // الاحتفاظ بالجلسة في الذاكرة مع تحديث الحالة
                if (session) {
                    session.status = hasAuthFiles || isProtected ? 'saved' : 'auth_required';
                    session.lastUpdated = new Date().toISOString();
                    session.client = null; // إزالة مرجع العميل فقط
                    console.log(`💾 Preserved session ${sessionId} in memory with status: ${session.status}`);
                }
            }

            // تحديث فهرس الجلسات
            this.updateSessionsIndex();

        } catch (error) {
            console.error(`Error cleaning up session ${sessionId}:`, error);
        }
    }

    /**
     * الحصول على جميع الجلسات
     */
    getAllSessions() {
        const sessions = [];
        for (const [sessionId, sessionData] of this.sessions) {
            sessions.push({
                id: sessionId,
                status: sessionData.status,
                connectionType: sessionData.connectionType,
                phoneNumber: sessionData.phoneNumber,
                userInfo: sessionData.userInfo,
                createdAt: sessionData.createdAt,
                messageCount: sessionData.messageCount || 0,
                contactCount: sessionData.contactCount || 0,
                chatCount: sessionData.chatCount || 0
            });
        }
        return sessions;
    }

    /**
     * حفظ جلسة بشكل آمن - نسخة محسنة مع حماية إضافية
     */
    async saveSessionSafely(sessionId) {
        try {
            const session = this.sessions.get(sessionId);
            if (!session) {
                console.log(`Session ${sessionId} not found in memory`);
                return;
            }

            console.log(`Safely saving session ${sessionId}...`);
            
            // التأكد من وجود مجلد الجلسة
            const sessionPath = path.join(this.sessionsPath, sessionId);
            this.ensureDirectoryExists(sessionPath);
            
            // تحديث معلومات الجلسة
            session.lastSaved = new Date().toISOString();
            session.lastUpdated = new Date().toISOString();
            session.status = session.status === 'connected' ? 'saved' : session.status;
            session.connectionCount = (session.connectionCount || 0) + 1;
            
            // حفظ معلومات الجلسة
            await this.saveSession(sessionId);
            
            // إنشاء ملف حماية لمنع الحذف
            const protectionFile = path.join(sessionPath, '.session-protected');
            const protectionData = {
                protected: true,
                savedAt: new Date().toISOString(),
                sessionId: sessionId,
                version: '1.0',
                authFiles: this.checkSessionAuthFiles(sessionPath, sessionId)
            };
            
            fs.writeFileSync(protectionFile, JSON.stringify(protectionData, null, 2), 'utf8');
            
            console.log(`Session ${sessionId} saved and protected successfully`);
        } catch (error) {
            console.error(`Error saving session ${sessionId}:`, error);
        }
    }

    /**
     * دالة جديدة لإنشاء جلسة محمية من الحذف
     */
    async createProtectedSession(sessionId, connectionType = 'qr', phoneNumber = null) {
        try {
            console.log(`Creating protected session: ${sessionId}`);
            
            // إنشاء الجلسة العادية
            const session = await this.createSession(sessionId, connectionType, phoneNumber);
            
            if (session && session.success) {
                // حفظ الجلسة بشكل آمن
                await this.saveSessionSafely(sessionId);
                
                console.log(`Protected session ${sessionId} created successfully`);
                return session;
            }
            
            return session;
        } catch (error) {
            console.error(`Error creating protected session ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * دالة للتحقق من حماية الجلسة
     */
    isSessionProtected(sessionId) {
        try {
            const sessionPath = path.join(this.sessionsPath, sessionId);
            const protectionFile = path.join(sessionPath, '.session-protected');
            
            if (fs.existsSync(protectionFile)) {
                const protectionData = JSON.parse(fs.readFileSync(protectionFile, 'utf8'));
                return protectionData.protected === true;
            }
            
            // فحص وجود ملفات المصادقة كحماية إضافية
            return this.checkSessionAuthFiles(sessionPath, sessionId);
        } catch (error) {
            console.error(`Error checking session protection for ${sessionId}:`, error);
            return false;
        }
    }

    /**
     * دالة لإعادة المصادقة للجلسات المحفوظة
     */
    async reauthenticateSession(sessionId) {
        try {
            console.log(`Attempting to reauthenticate session: ${sessionId}`);
            
            const sessionPath = path.join(this.sessionsPath, sessionId);
            
            // التحقق من وجود ملفات المصادقة
            if (!this.checkSessionAuthFiles(sessionPath, sessionId)) {
                console.log(`No authentication files found for ${sessionId}, creating new session`);
                return await this.createProtectedSession(sessionId);
            }
            
            // محاولة إعادة الاتصال بالجلسة الموجودة
            const session = this.sessions.get(sessionId);
            if (session) {
                session.status = 'reconnecting';
                session.lastReconnectAttempt = new Date().toISOString();
            }
            
            // إنشاء عميل جديد للجلسة الموجودة
            const client = new Client({
                authStrategy: new LocalAuth({ 
                    clientId: sessionId,
                    dataPath: sessionPath
                }),
                puppeteer: {
                    headless: true,
                    args: [
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--disable-gpu'
                    ]
                }
            });
            
            // إعداد مستمعي الأحداث
            this.setupClientEvents(client, sessionId);
            
            // حفظ العميل
            this.clients.set(sessionId, client);
            
            // بدء العميل
            await client.initialize();
            
            console.log(`Reauthentication started for session ${sessionId}`);
            return { success: true, message: 'إعادة المصادقة قيد التقدم' };
            
        } catch (error) {
            console.error(`Error reauthenticating session ${sessionId}:`, error);
            return { success: false, error: error.message };
        }
    }





    /**
     * إغلاق جميع الجلسات
     */
    async shutdown() {
        console.log('Shutting down WhatsApp Manager...');

        try {
            // إيقاف مراقبة الجلسات
            this.stopSessionMonitoring();
            
            // حفظ جميع الجلسات المتصلة قبل الإغلاق
            this.updateSessionsIndex();

            // حفظ جميع الجلسات بشكل آمن أولاً
            const savePromises = [];
            for (const [sessionId] of this.sessions) {
                savePromises.push(this.saveSessionSafely(sessionId));
            }
            await Promise.all(savePromises);

            // إغلاق جميع العملاء بشكل آمن بدون حذف الملفات
            const shutdownPromises = [];
            for (const [sessionId, client] of this.clients) {
                shutdownPromises.push(
                    new Promise(async (resolve) => {
                        try {
                            console.log(`Shutting down session: ${sessionId}`);

                            // إغلاق العميل بدون تسجيل الخروج للحفاظ على ملفات الجلسة
                            // استخدام timeout لتجنب التعليق
                            const destroyPromise = client.destroy();
                            const timeoutPromise = new Promise((_, reject) => 
                                setTimeout(() => reject(new Error('Destroy timeout')), 5000)
                            );
                            
                            try {
                                await Promise.race([destroyPromise, timeoutPromise]);
                            } catch (destroyError) {
                                console.warn(`Force closing session ${sessionId}:`, destroyError.message);
                            }

                            // إزالة العميل من الذاكرة فقط (بدون حذف الملفات)
                            this.clients.delete(sessionId);

                            console.log(`Session ${sessionId} closed and saved successfully`);
                        } catch (error) {
                            console.error(`Error shutting down session ${sessionId}:`, error);
                        } finally {
                            resolve();
                        }
                    })
                );
            }

            // انتظار إغلاق جميع الجلسات مع timeout
            const allShutdownPromise = Promise.all(shutdownPromises);
            const timeoutPromise = new Promise((resolve) => 
                setTimeout(() => {
                    console.warn('Shutdown timeout reached, forcing exit');
                    resolve();
                }, 10000)
            );
            
            await Promise.race([allShutdownPromise, timeoutPromise]);

            // حفظ نهائي للجلسات مع الاحتفاظ بجميع البيانات
            this.updateSessionsIndex();

            console.log('WhatsApp Manager shutdown complete - all sessions saved');

        } catch (error) {
            console.error('Error during WhatsApp Manager shutdown:', error);
        }
    }
}

module.exports = WhatsAppManagerWeb;
